import { createRouter, createWebHistory } from 'vue-router'
import axios from 'axios'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/auth/LoginView.vue'),
      meta: { requiresAuth: false },
    },
    {
      path: '/',
      name: 'root',
      component: () => import('@/views/home/<USER>'),
      beforeEnter: (to, from, next) => {
        const role = localStorage.getItem('userRole')
        if (role === 'operation') {
          next('/operation-home')
        } else if (role === 'admin') {
          next('/admin/dashboard')
        } else {
          next() // 默认显示商务首页
        }
      },
      meta: { requiresAuth: true },
    },
    {
      path: '/operation-home',
      name: 'operationHome',
      component: () => import('@/views/home/<USER>'),
      meta: { requiresAuth: true, requiresOperation: true }, // 运营首页
    },
    // 管理员后台
    {
      path: '/admin',
      name: 'admin',
      component: () => import('@/views/admin/AdminLayout.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
      children: [
        {
          path: 'business-users',
          name: 'businessUsers',
          component: () => import('@/views/admin/BusinessUsersView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'dashboard',
          name: 'adminDashboard',
          component: () => import('@/views/admin/DashboardView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'profile',
          name: 'adminProfile',
          component: () => import('@/views/profile/AdminProfileView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        // 管理员访问业务模块的路由
        {
          path: 'talent/public',
          name: 'adminTalentPublic',
          component: () => import('@/views/talent/PublicTalentView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'talent/exclusive',
          name: 'adminTalentExclusive',
          component: () => import('@/views/talent/ExclusiveTalentView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'talent/special',
          name: 'adminTalentSpecial',
          component: () => import('@/views/talent/SpecialTalentView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'talent/shared',
          name: 'adminTalentShared',
          component: () => import('@/views/talent/SharedTalentView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'team/public',
          name: 'adminTeamPublic',
          component: () => import('@/views/team/PublicTeamLeaderView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'team/exclusive',
          name: 'adminTeamExclusive',
          component: () => import('@/views/team/ExclusiveTeamLeaderView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'team/special',
          name: 'adminTeamSpecial',
          component: () => import('@/views/team/SpecialTeamLeaderView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'team/shared',
          name: 'adminTeamShared',
          component: () => import('@/views/team/SharedTeamLeaderView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'product/list',
          name: 'adminProductList',
          component: () => import('@/views/product/ProductListView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'product/hidden',
          name: 'adminProductHidden',
          component: () => import('@/views/product/HiddenProductView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'product/hot-ranking',
          name: 'adminProductHotRanking',
          component: () => import('@/views/product/HotRankingView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'order/search',
          name: 'adminOrderSearch',
          component: () => import('@/views/order/OrderSearchView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'order/merchant-commission',
          name: 'adminMerchantCommissionOrder',
          component: () => import('@/views/order/MerchantCommissionOrderView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'order/reserved-service-fee',
          name: 'adminReservedServiceFeeOrder',
          component: () => import('@/views/order/ReservedServiceFeeOrderView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'order/team-leader-commission',
          name: 'adminTeamLeaderCommissionOrder',
          component: () => import('@/views/order/TeamLeaderCommissionOrderView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'boost/management',
          name: 'adminBoostManagement',
          component: () => import('@/views/boost/BoostManagementView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },

        {
          path: 'performance-overview',
          name: 'adminPerformanceOverview',
          component: () => import('@/views/performance/BusinessPerformanceOverviewView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'performance-statistics',
          name: 'adminPerformanceStatistics',
          component: () => import('@/views/performance/BusinessPerformanceStatisticsView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'talent-statistics',
          name: 'adminTalentStatistics',
          component: () => import('@/views/performance/PerformanceStatisticsView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'sample/management',
          name: 'adminSampleManagement',
          component: () => import('@/views/sample/SampleManagementView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'system/config',
          name: 'adminSystemConfig',
          component: () => import('@/views/admin/SystemConfigView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
      ],
    },
    // 达人管理
    {
      path: '/talent',
      name: 'talent',
      redirect: '/talent/public',
      meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
      children: [
        {
          path: 'public',
          name: 'publicTalent',
          component: () => import('@/views/talent/PublicTalentView.vue'),
          meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
        },
        {
          path: 'exclusive',
          name: 'exclusiveTalent',
          component: () => import('@/views/talent/ExclusiveTalentView.vue'),
          meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
        },
        {
          path: 'special',
          name: 'specialTalent',
          component: () => import('@/views/talent/SpecialTalentView.vue'),
          meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
        },
        {
          path: 'shared',
          name: 'sharedTalent',
          component: () => import('@/views/talent/SharedTalentView.vue'),
          meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
        },
      ],
    },
    // 团长管理
    {
      path: '/team',
      name: 'team',
      redirect: '/team/public',
      meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
      children: [
        {
          path: 'public',
          name: 'publicTeamLeader',
          component: () => import('@/views/team/PublicTeamLeaderView.vue'),
          meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
        },
        {
          path: 'exclusive',
          name: 'exclusiveTeamLeader',
          component: () => import('@/views/team/ExclusiveTeamLeaderView.vue'),
          meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
        },
        {
          path: 'special',
          name: 'specialTeamLeader',
          component: () => import('@/views/team/SpecialTeamLeaderView.vue'),
          meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
        },
        {
          path: 'shared',
          name: 'sharedTeamLeader',
          component: () => import('@/views/team/SharedTeamLeaderView.vue'),
          meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
        },
      ],
    },
    // 商品管理
    {
      path: '/product/list',
      name: 'productList',
      component: () => import('@/views/product/ProductListView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/product/my-promotion',
      name: 'myPromotion',
      component: () => import('@/views/product/MyPromotionView.vue'),
      meta: { requiresAuth: true, requiresBusiness: true },
    },
    {
      path: '/product/hot-ranking',
      name: 'productHotRanking',
      component: () => import('@/views/product/HotRankingView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/product/hidden',
      name: 'hiddenProduct',
      component: () => import('@/views/product/HiddenProductView.vue'),
      meta: { requiresAuth: true },
    },
    // 订单管理
    {
      path: '/order/search',
      name: 'orderSearch',
      component: () => import('@/views/order/OrderSearchView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/order/merchant-commission',
      name: 'merchantCommissionOrder',
      component: () => import('@/views/order/MerchantCommissionOrderView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/order/reserved-service-fee',
      name: 'reservedServiceFeeOrder',
      component: () => import('@/views/order/ReservedServiceFeeOrderView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/order/team-leader-commission',
      name: 'teamLeaderCommissionOrder',
      component: () => import('@/views/order/TeamLeaderCommissionOrderView.vue'),
      meta: { requiresAuth: true },
    },
    // 助播管理
    {
      path: '/boost',
      name: 'boost',
      redirect: '/boost/management',
      meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
      children: [
        {
          path: 'management',
          name: 'boostManagement',
          component: () => import('@/views/boost/BoostManagementView.vue'),
          meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
        },
      ],
    },
    // 商务业绩概览
    {
      path: '/performance-overview',
      name: 'performanceOverview',
      component: () => import('@/views/performance/BusinessPerformanceOverviewView.vue'),
      meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
    },
    // 商务业绩统计
    {
      path: '/performance-statistics',
      name: 'performanceStatistics',
      component: () => import('@/views/performance/BusinessPerformanceStatisticsView.vue'),
      meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
    },
    // 达人业绩统计
    {
      path: '/talent-statistics',
      name: 'talentStatistics',
      component: () => import('@/views/performance/PerformanceStatisticsView.vue'),
      meta: { requiresAuth: true, roles: ['business', 'operation', 'admin'] },
    },
    // 普通用户个人设置
    {
      path: '/profile',
      name: 'profile',
      component: () => import('@/views/profile/ProfileView.vue'),
      meta: { requiresAuth: true },
    },
    // 寄样管理
    {
      path: '/sample/management',
      name: 'SampleManagement',
      component: () => import('../views/sample/SampleManagementView.vue'),
      meta: {
        requiresAuth: true,
        title: '寄样管理',
        roles: ['business', 'operation', 'admin'],
      },
    },
  ],
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 检查路由是否需要认证
  if (to.meta.requiresAuth !== false) {
    // 检查是否有令牌
    const token = localStorage.getItem('token')
    if (!token) {
      // 如果没有令牌，重定向到登录页
      next({ name: 'login' })
      return
    }

    // 验证令牌有效性
    try {
      const response = await axios.get('/api/auth/check')
      const user = response.data.data.user

      console.log('当前用户角色:', user.role)

      // 存储用户角色和头像到 localStorage
      localStorage.setItem('userRole', user.role)
      if (user.avatar) {
        localStorage.setItem('userAvatar', user.avatar)
      }

      // 检查是否需要管理员权限
      if (to.meta.requiresAdmin && user.role !== 'admin') {
        ElMessage.error('权限不足，无法访问该页面')
        if (user.role === 'operation') {
          next({ name: 'operationHome' })
        } else {
          next({ name: 'home' })
        }
        return
      }

      // 检查是否需要商务权限
      if (to.meta.requiresBusiness && user.role !== 'business' && user.role !== 'admin') {
        // 如果是运营用户点击首页，重定向到运营首页而不是显示错误
        if (user.role === 'operation' && to.path === '/') {
          next({ name: 'operationHome' })
          return
        }

        ElMessage.error('权限不足，无法访问该页面')
        if (user.role === 'operation') {
          next({ name: 'operationHome' })
        } else {
          next({ name: 'adminDashboard' })
        }
        return
      }

      // 检查是否需要运营权限
      if (to.meta.requiresOperation && user.role !== 'operation' && user.role !== 'admin') {
        ElMessage.error('权限不足，无法访问该页面')
        next({ name: 'home' })
        return
      }

      // 检查角色权限（新的权限控制方式）
      if (to.meta.roles && Array.isArray(to.meta.roles)) {
        if (!to.meta.roles.includes(user.role)) {
          ElMessage.error('权限不足，无法访问该页面')
          if (user.role === 'operation') {
            next({ name: 'operationHome' })
          } else if (user.role === 'admin') {
            next({ name: 'adminDashboard' })
          } else {
            next({ name: 'home' })
          }
          return
        }
      }
    } catch (error) {
      // 令牌无效或已过期
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('userRole')
      localStorage.removeItem('userAvatar')
      ElMessage.error('登录已过期或无效，请重新登录')
      next({ name: 'login' })
      return
    }
  }

  // 如果已登录且访问登录页，重定向到首页
  if (to.name === 'login' && localStorage.getItem('token')) {
    try {
      const response = await axios.get('/api/auth/check')
      const user = response.data.data.user
      const role = user.role

      // 根据用户角色重定向到不同页面
      if (role === 'admin') {
        next({ name: 'adminDashboard' })
      } else if (role === 'operation') {
        next({ name: 'operationHome' })
      } else {
        next({ name: 'home' })
      }
      return
    } catch (error) {
      // 令牌无效，清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('userRole')
      localStorage.removeItem('userAvatar')
    }
  }

  next()
})

export default router
