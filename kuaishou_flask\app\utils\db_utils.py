import mysql.connector
from mysql.connector import Error, pooling
from app.config.config import DB_CONFIG

# 创建连接池
try:
    connection_pool = mysql.connector.pooling.MySQLConnectionPool(**DB_CONFIG)
    print("数据库连接池创建成功")
except Error as e:
    print(f"数据库连接池创建失败: {e}")
    raise

def get_connection():
    """获取数据库连接"""
    try:
        return connection_pool.get_connection()
    except Error as e:
        print(f"获取数据库连接失败: {e}")
        raise 