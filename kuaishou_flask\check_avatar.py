#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app.utils.db_utils import get_connection

def check_avatar_data():
    connection = get_connection()
    cursor = connection.cursor(dictionary=True)
    
    # 查看talent表中有头像的数据
    print("=== talent表中的头像数据 ===")
    cursor.execute("SELECT talent_name, avatar_url FROM talent WHERE avatar_url IS NOT NULL AND avatar_url != '' LIMIT 10")
    talent_results = cursor.fetchall()
    
    for row in talent_results:
        print(f"达人: {row['talent_name']}, 头像: {row['avatar_url']}")
    
    print(f"\n总共有 {len(talent_results)} 个达人有头像数据")
    
    # 查看promoter_performance表中的数据
    print("\n=== promoter_performance表中的达人名称 ===")
    cursor.execute("SELECT DISTINCT promoter_name FROM promoter_performance LIMIT 10")
    performance_results = cursor.fetchall()
    
    for row in performance_results:
        print(f"业绩表中的达人: {row['promoter_name']}")
    
    # 检查关联情况
    print("\n=== 检查关联情况 ===")
    cursor.execute("""
        SELECT 
            pp.promoter_name,
            t.talent_name,
            t.avatar_url
        FROM promoter_performance pp
        LEFT JOIN talent t ON pp.promoter_name = t.talent_name
        WHERE pp.promoter_name IS NOT NULL
        GROUP BY pp.promoter_name, t.talent_name, t.avatar_url
        LIMIT 10
    """)
    join_results = cursor.fetchall()
    
    for row in join_results:
        print(f"业绩表达人: {row['promoter_name']}, 关联到talent表: {row['talent_name']}, 头像: {row['avatar_url']}")
    
    # 检查表结构
    print("\n=== talent表结构 ===")
    cursor.execute("DESCRIBE talent")
    talent_columns = cursor.fetchall()
    for col in talent_columns:
        print(f"{col['Field']}: {col['Type']}")

    print("\n=== promoter_performance表结构 ===")
    cursor.execute("DESCRIBE promoter_performance")
    performance_columns = cursor.fetchall()
    for col in performance_columns:
        print(f"{col['Field']}: {col['Type']}")

    # 检查是否有其他可以关联的字段
    print("\n=== 检查promoter_id关联 ===")
    cursor.execute("""
        SELECT
            pp.promoter_id,
            pp.promoter_name,
            t.talent_id,
            t.talent_name,
            t.avatar_url
        FROM promoter_performance pp
        LEFT JOIN talent t ON pp.promoter_id = t.talent_id
        WHERE pp.promoter_id IS NOT NULL
        GROUP BY pp.promoter_id, pp.promoter_name, t.talent_id, t.talent_name, t.avatar_url
        LIMIT 10
    """)
    id_join_results = cursor.fetchall()

    for row in id_join_results:
        print(f"业绩表ID: {row['promoter_id']}, 业绩表达人: {row['promoter_name']}, 关联到talent表ID: {row['talent_id']}, talent表达人: {row['talent_name']}, 头像: {row['avatar_url']}")

    cursor.close()
    connection.close()

if __name__ == "__main__":
    check_avatar_data()
