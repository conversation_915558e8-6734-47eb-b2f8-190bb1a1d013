<template>
  <div class="business-users-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>{{ getPageTitle }}</h3>
          <div class="header-actions">
            <el-select
              v-model="roleFilter"
              placeholder="角色筛选"
              style="width: 120px; margin-right: 15px"
            >
              <el-option label="全部" value="" />
              <el-option label="商务" value="business" />
              <el-option label="运营" value="operation" />
              <el-option label="管理员" value="admin" />
            </el-select>
            <el-button type="primary" @click="openAddUserDialog">添加用户</el-button>
          </div>
        </div>
      </template>

      <el-table v-loading="loading" :data="userList" style="width: 100%" border>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="账号" width="150" />
        <el-table-column prop="name" label="姓名" width="150" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="scope">
            <el-tag :type="getRoleTagType(scope.row.role)">{{
              getRoleName(scope.row.role)
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180" />
        <el-table-column prop="update_time" label="更新时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.username !== 'manager'"
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              v-if="scope.row.username !== 'manager'"
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
      width="600px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
        style="max-width: 400px"
      >
        <el-form-item label="账号" prop="username" v-if="dialogType === 'add'">
          <el-input v-model="userForm.username" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialogType === 'add'">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <!-- 编辑用户时的密码管理 -->
        <div v-if="dialogType === 'edit'">
          <el-form-item label="密码" prop="newPassword">
            <el-input
              v-model="userForm.newPassword"
              type="password"
              placeholder="直接输入新密码即可修改，留空则不修改"
              show-password
              clearable
            />
            <div style="margin-top: 5px; color: #999; font-size: 12px">
              当前密码：
              <span style="font-family: monospace">
                <el-input
                  v-model="currentPassword"
                  :type="showCurrentPassword ? 'text' : 'password'"
                  size="small"
                  readonly
                  style="
                    width: 200px;
                    display: inline-block;
                    background-color: transparent;
                    border: none;
                  "
                >
                  <template #suffix>
                    <el-icon
                      @click="showCurrentPassword = !showCurrentPassword"
                      style="cursor: pointer"
                    >
                      <View v-if="!showCurrentPassword" />
                      <Hide v-else />
                    </el-icon>
                  </template>
                </el-input>
              </span>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="商务" value="business" />
            <el-option label="运营" value="operation" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>

        <!-- 权限设置 - 只有商务和运营角色才显示 -->
        <div v-if="userForm.role === 'business' || userForm.role === 'operation'">
          <el-divider content-position="left">权限设置</el-divider>

          <!-- 权限配置面板 -->
          <div class="permissions-panel">
            <!-- 达人管理权限 -->
            <el-card class="permission-card" shadow="never">
              <template #header>
                <div class="permission-header">
                  <el-switch
                    v-model="userForm.perm_talent"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleModulePermissionChange('talent', $event)"
                  />
                  <span class="permission-title">达人管理</span>
                  <el-button
                    v-if="userForm.perm_talent"
                    type="text"
                    size="small"
                    @click="togglePermissionDetail('talent')"
                  >
                    {{ expandedPermissions.talent ? '收起' : '展开' }}
                    <el-icon
                      ><ArrowDown v-if="!expandedPermissions.talent" /><ArrowUp v-else
                    /></el-icon>
                  </el-button>
                </div>
              </template>
              <el-collapse-transition>
                <div
                  v-show="userForm.perm_talent && expandedPermissions.talent"
                  class="permission-detail"
                >
                  <div class="permission-section">
                    <h4>数据权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.talent.data">
                      <el-checkbox label="own_only" v-if="userForm.role === 'business'"
                        >仅本人数据</el-checkbox
                      >
                      <el-checkbox label="all_data" v-if="userForm.role === 'operation'"
                        >全部数据</el-checkbox
                      >
                    </el-checkbox-group>
                  </div>
                  <div class="permission-section">
                    <h4>操作权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.talent.operations">
                      <el-checkbox label="add_talent">添加达人</el-checkbox>
                      <el-checkbox label="edit_talent_info">编辑达人信息</el-checkbox>
                      <el-checkbox label="edit_talent_type" v-if="userForm.role === 'operation'"
                        >编辑达人类型和归属</el-checkbox
                      >
                    </el-checkbox-group>
                  </div>
                </div>
              </el-collapse-transition>
            </el-card>

            <!-- 团长管理权限 -->
            <el-card class="permission-card" shadow="never">
              <template #header>
                <div class="permission-header">
                  <el-switch
                    v-model="userForm.perm_team_leader"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleModulePermissionChange('team_leader', $event)"
                  />
                  <span class="permission-title">团长管理</span>
                  <el-button
                    v-if="userForm.perm_team_leader"
                    type="text"
                    size="small"
                    @click="togglePermissionDetail('team_leader')"
                  >
                    {{ expandedPermissions.team_leader ? '收起' : '展开' }}
                    <el-icon
                      ><ArrowDown v-if="!expandedPermissions.team_leader" /><ArrowUp v-else
                    /></el-icon>
                  </el-button>
                </div>
              </template>
              <el-collapse-transition>
                <div
                  v-show="userForm.perm_team_leader && expandedPermissions.team_leader"
                  class="permission-detail"
                >
                  <div class="permission-section">
                    <h4>数据权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.team_leader.data">
                      <el-checkbox label="own_only" v-if="userForm.role === 'business'"
                        >仅本人数据</el-checkbox
                      >
                      <el-checkbox label="all_data" v-if="userForm.role === 'operation'"
                        >全部数据</el-checkbox
                      >
                    </el-checkbox-group>
                  </div>
                  <div class="permission-section">
                    <h4>操作权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.team_leader.operations">
                      <el-checkbox label="add_team_leader">添加团长</el-checkbox>
                      <el-checkbox label="edit_team_leader_info">编辑团长信息</el-checkbox>
                      <el-checkbox
                        label="edit_team_leader_type"
                        v-if="userForm.role === 'operation'"
                        >编辑团长类型和归属</el-checkbox
                      >
                    </el-checkbox-group>
                  </div>
                </div>
              </el-collapse-transition>
            </el-card>

            <!-- 商品管理权限 -->
            <el-card class="permission-card" shadow="never">
              <template #header>
                <div class="permission-header">
                  <el-switch
                    v-model="userForm.perm_product"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleModulePermissionChange('product', $event)"
                  />
                  <span class="permission-title">商品管理</span>
                  <el-button
                    v-if="userForm.perm_product"
                    type="text"
                    size="small"
                    @click="togglePermissionDetail('product')"
                  >
                    {{ expandedPermissions.product ? '收起' : '展开' }}
                    <el-icon
                      ><ArrowDown v-if="!expandedPermissions.product" /><ArrowUp v-else
                    /></el-icon>
                  </el-button>
                </div>
              </template>
              <el-collapse-transition>
                <div
                  v-show="userForm.perm_product && expandedPermissions.product"
                  class="permission-detail"
                >
                  <div class="permission-section">
                    <h4>操作权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.product.operations">
                      <el-checkbox label="add_promotion" v-if="userForm.role === 'business'"
                        >添加推广</el-checkbox
                      >
                      <el-checkbox label="quick_sample" v-if="userForm.role === 'business'"
                        >快速申样</el-checkbox
                      >
                      <el-checkbox label="edit_rating" v-if="userForm.role === 'operation'"
                        >编辑评分</el-checkbox
                      >
                      <el-checkbox label="edit_rate" v-if="userForm.role === 'operation'"
                        >编辑费率</el-checkbox
                      >
                      <el-checkbox label="hide_product" v-if="userForm.role === 'operation'"
                        >隐藏商品</el-checkbox
                      >
                      <el-checkbox label="view_promotion_info" v-if="userForm.role === 'operation'"
                        >查看推广信息</el-checkbox
                      >
                      <el-checkbox label="manage_tags" v-if="userForm.role === 'operation'"
                        >管理标签</el-checkbox
                      >
                      <el-checkbox label="sync_data" v-if="userForm.role === 'operation'"
                        >同步数据</el-checkbox
                      >
                    </el-checkbox-group>
                  </div>
                </div>
              </el-collapse-transition>
            </el-card>

            <!-- 订单管理权限 -->
            <el-card class="permission-card" shadow="never">
              <template #header>
                <div class="permission-header">
                  <el-switch
                    v-model="userForm.perm_order"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleModulePermissionChange('order', $event)"
                  />
                  <span class="permission-title">订单管理</span>
                  <el-button
                    v-if="userForm.perm_order"
                    type="text"
                    size="small"
                    @click="togglePermissionDetail('order')"
                  >
                    {{ expandedPermissions.order ? '收起' : '展开' }}
                    <el-icon
                      ><ArrowDown v-if="!expandedPermissions.order" /><ArrowUp v-else
                    /></el-icon>
                  </el-button>
                </div>
              </template>
              <el-collapse-transition>
                <div
                  v-show="userForm.perm_order && expandedPermissions.order"
                  class="permission-detail"
                >
                  <div class="permission-section">
                    <h4>子模块权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.order.modules">
                      <el-checkbox label="merchant_commission_orders">商家佣金订单</el-checkbox>
                      <el-checkbox label="reserved_service_fee_orders">预留服务费订单</el-checkbox>
                      <el-checkbox label="team_leader_commission_orders">团长佣金订单</el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <div class="permission-section">
                    <h4>数据权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.order.data">
                      <el-checkbox label="own_only" v-if="userForm.role === 'business'"
                        >仅本人数据</el-checkbox
                      >
                      <el-checkbox label="all_data" v-if="userForm.role === 'operation'"
                        >全部数据</el-checkbox
                      >
                    </el-checkbox-group>
                  </div>
                  <div class="permission-section" v-if="userForm.role === 'operation'">
                    <h4>操作权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.order.operations">
                      <el-checkbox label="edit_business">编辑商务</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </el-collapse-transition>
            </el-card>

            <!-- 助播管理权限 -->
            <el-card class="permission-card" shadow="never">
              <template #header>
                <div class="permission-header">
                  <el-switch
                    v-model="userForm.perm_boost"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleModulePermissionChange('boost', $event)"
                  />
                  <span class="permission-title">助播管理</span>
                  <el-button
                    v-if="userForm.perm_boost"
                    type="text"
                    size="small"
                    @click="togglePermissionDetail('boost')"
                  >
                    {{ expandedPermissions.boost ? '收起' : '展开' }}
                    <el-icon
                      ><ArrowDown v-if="!expandedPermissions.boost" /><ArrowUp v-else
                    /></el-icon>
                  </el-button>
                </div>
              </template>
              <el-collapse-transition>
                <div
                  v-show="userForm.perm_boost && expandedPermissions.boost"
                  class="permission-detail"
                >
                  <div class="permission-section">
                    <h4>数据权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.boost.data">
                      <el-checkbox label="">不限制（全部数据）</el-checkbox>
                      <el-checkbox label="business_self">仅本人申请</el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <div class="permission-section">
                    <h4>操作权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.boost.operations">
                      <el-checkbox label="view">查看</el-checkbox>
                      <el-checkbox label="add">新增申请</el-checkbox>
                      <el-checkbox label="edit">编辑</el-checkbox>
                      <el-checkbox label="approve">审批</el-checkbox>
                      <el-checkbox label="settle">结算</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </el-collapse-transition>
            </el-card>

            <!-- 业绩管理权限 -->
            <el-card class="permission-card" shadow="never">
              <template #header>
                <div class="permission-header">
                  <el-switch
                    v-model="userForm.perm_performance_overview"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleModulePermissionChange('performance', $event)"
                  />
                  <span class="permission-title">业绩管理</span>
                  <el-button
                    v-if="
                      userForm.perm_performance_overview || userForm.perm_performance_statistics
                    "
                    type="text"
                    size="small"
                    @click="togglePermissionDetail('performance')"
                  >
                    {{ expandedPermissions.performance ? '收起' : '展开' }}
                    <el-icon
                      ><ArrowDown v-if="!expandedPermissions.performance" /><ArrowUp v-else
                    /></el-icon>
                  </el-button>
                </div>
              </template>
              <el-collapse-transition>
                <div
                  v-show="
                    (userForm.perm_performance_overview || userForm.perm_performance_statistics) &&
                    expandedPermissions.performance
                  "
                  class="permission-detail"
                >
                  <div class="permission-section">
                    <h4>子模块权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.performance.modules">
                      <el-checkbox label="performance_overview">业绩概览</el-checkbox>
                      <el-checkbox label="performance_statistics">业绩统计</el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <div class="permission-section">
                    <h4>数据权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.performance.data">
                      <el-checkbox label="own_only" v-if="userForm.role === 'business'"
                        >仅本人数据</el-checkbox
                      >
                      <el-checkbox label="all_data" v-if="userForm.role === 'operation'"
                        >全部数据</el-checkbox
                      >
                    </el-checkbox-group>
                  </div>
                </div>
              </el-collapse-transition>
            </el-card>

            <!-- 寄样管理权限 -->
            <el-card class="permission-card" shadow="never">
              <template #header>
                <div class="permission-header">
                  <el-switch
                    v-model="userForm.perm_sample_management"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleModulePermissionChange('sample', $event)"
                  />
                  <span class="permission-title">寄样管理</span>
                  <el-button
                    v-if="userForm.perm_sample_management"
                    type="text"
                    size="small"
                    @click="togglePermissionDetail('sample')"
                  >
                    {{ expandedPermissions.sample ? '收起' : '展开' }}
                    <el-icon
                      ><ArrowDown v-if="!expandedPermissions.sample" /><ArrowUp v-else
                    /></el-icon>
                  </el-button>
                </div>
              </template>
              <el-collapse-transition>
                <div
                  v-show="userForm.perm_sample_management && expandedPermissions.sample"
                  class="permission-detail"
                >
                  <div class="permission-section">
                    <h4>数据权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.sample.data">
                      <el-checkbox label="">不限制（全部数据）</el-checkbox>
                      <el-checkbox label="business_self">仅本人寄样</el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <div class="permission-section">
                    <h4>操作权限</h4>
                    <el-checkbox-group v-model="userForm.permissions.sample.operations">
                      <el-checkbox label="view">查看</el-checkbox>
                      <el-checkbox label="add">新增</el-checkbox>
                      <el-checkbox label="edit">编辑</el-checkbox>
                      <el-checkbox label="delete">删除</el-checkbox>
                      <el-checkbox label="ship">发货</el-checkbox>
                      <el-checkbox label="track">跟踪</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </el-collapse-transition>
            </el-card>
          </div>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUserForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { View, Hide, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'

// 权限数据结构
interface ModulePermission {
  data: string[]
  operations: string[]
}

interface OrderModulePermission {
  data: string[]
  operations: string[]
  modules: string[]
}

interface PerformanceModulePermission {
  data: string[]
  operations: string[]
  modules: string[]
}

interface UserPermissions {
  talent: ModulePermission
  team_leader: ModulePermission
  product: ModulePermission
  order: OrderModulePermission
  boost: ModulePermission
  performance: PerformanceModulePermission
  sample: ModulePermission
}

interface User {
  id: number
  username: string
  name: string
  role: string
  create_time: string
  update_time: string
  // 权限字段
  perm_talent?: number
  perm_team_leader?: number
  perm_product?: number
  perm_order?: number
  perm_boost?: number
  perm_performance_overview?: number
  perm_performance_statistics?: number
  perm_sample_management?: number
  perm_hide_product?: number
  perm_manage_tags?: number
  perm_merchant_commission_orders?: number
  perm_reserved_service_fee_orders?: number
  perm_team_leader_commission_orders?: number
  permissions?: UserPermissions
}

const route = useRoute()
const router = useRouter()

// 用户列表
const userList = ref<User[]>([])
const loading = ref(false)

// 角色过滤
const roleFilter = ref('')

// 对话框相关
const userDialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const userFormRef = ref<FormInstance>()
const submitting = ref(false)

// 页面标题
const getPageTitle = computed(() => {
  if (roleFilter.value === 'business') {
    return '商务用户管理'
  } else if (roleFilter.value === 'operation') {
    return '运营用户管理'
  } else {
    return '用户管理'
  }
})

// 用户表单
const userForm = reactive({
  id: 0,
  username: '',
  name: '',
  password: '',
  newPassword: '',
  role: 'business',
  // 权限字段
  perm_talent: 1,
  perm_team_leader: 1,
  perm_product: 1,
  perm_order: 1,
  perm_boost: 1,
  perm_performance_overview: 1,
  perm_performance_statistics: 1,
  perm_sample_management: 1,
  perm_hide_product: 0, // 隐藏商品权限默认为0
  perm_manage_tags: 0, // 标签管理权限默认为0
  perm_merchant_commission_orders: 1, // 商家返佣订单权限默认为1
  perm_reserved_service_fee_orders: 1, // 预留服务费订单权限默认为1
  perm_team_leader_commission_orders: 1, // 团长返佣订单权限默认为1
  // 细粒度权限配置
  permissions: {
    talent: { data: [], operations: ['view', 'add', 'edit', 'assign'] },
    team_leader: { data: [], operations: ['view', 'add', 'edit', 'assign'] },
    product: { data: [], operations: ['view', 'add', 'edit', 'promote'] },
    order: { data: [], operations: [], modules: [] },
    boost: { data: [], operations: ['view', 'add', 'edit'] },
    performance: { data: [], operations: [], modules: [] },
    sample: { data: [], operations: ['view', 'add', 'edit'] },
    system: { data: [], operations: [] },
  } as UserPermissions,
})

// 权限展开状态
const expandedPermissions = reactive({
  talent: false,
  team_leader: false,
  product: false,
  order: false,
  boost: false,
  performance: false,
  sample: false,
})

// 密码相关状态
const currentPassword = ref('')
const showCurrentPassword = ref(false)

// 表单验证规则
const userRules = reactive({
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
  ],
  newPassword: [{ min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
})

// 获取角色名称
const getRoleName = (role: string): string => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'business':
      return '商务'
    case 'operation':
      return '运营'
    default:
      return '未知'
  }
}

// 获取角色标签类型
const getRoleTagType = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'business':
      return 'primary'
    case 'operation':
      return 'success'
    default:
      return 'info'
  }
}

// 获取用户列表
const getUserList = async () => {
  loading.value = true
  try {
    const params: Record<string, string> = {}
    if (roleFilter.value) {
      params['role'] = roleFilter.value
    }

    const response = await axios.get('/api/auth/business-users', { params })
    if (response.data.code === 0) {
      userList.value = response.data.data
    } else {
      ElMessage.error(response.data.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 打开添加用户对话框
const openAddUserDialog = () => {
  dialogType.value = 'add'
  userForm.id = 0
  userForm.username = ''
  userForm.name = ''
  userForm.password = ''
  userForm.newPassword = ''

  // 重置密码相关状态
  currentPassword.value = ''
  showCurrentPassword.value = false

  // 如果当前有角色过滤，则默认选择该角色
  userForm.role = roleFilter.value || 'business'
  // 重置权限为默认值
  userForm.perm_talent = 1
  userForm.perm_team_leader = 1
  userForm.perm_product = 1
  userForm.perm_order = 1
  userForm.perm_boost = 1
  userForm.perm_performance_overview = 1
  userForm.perm_performance_statistics = 1
  userForm.perm_sample_management = 1
  userForm.perm_hide_product = 0 // 隐藏商品权限默认为0
  userForm.perm_manage_tags = 0 // 标签管理权限默认为0
  userForm.perm_merchant_commission_orders = 1 // 商家返佣订单权限默认为1
  userForm.perm_reserved_service_fee_orders = 1 // 预留服务费订单权限默认为1
  userForm.perm_team_leader_commission_orders = 1 // 团长返佣订单权限默认为1

  // 重置细粒度权限
  userForm.permissions = {
    talent: { data: [], operations: [] },
    team_leader: { data: [], operations: [] },
    product: { data: [], operations: [] },
    order: { data: [], operations: [], modules: [] },
    boost: { data: [], operations: [] },
    performance: { data: [], operations: [], modules: [] },
    sample: { data: [], operations: [] },
  }

  // 重置权限展开状态
  Object.keys(expandedPermissions).forEach((key) => {
    expandedPermissions[key as keyof typeof expandedPermissions] = false
  })

  userDialogVisible.value = true
}

// 处理编辑用户
const handleEdit = async (row: User) => {
  try {
    dialogType.value = 'edit'

    // 获取用户详细信息（包括密码）
    const response = await axios.get(`/api/auth/business-users/${row.id}`)
    if (response.data.code === 0) {
      const userData = response.data.data
      userForm.id = userData.id
      userForm.username = userData.username
      userForm.name = userData.name
      userForm.role = userData.role

      // 设置当前密码
      currentPassword.value = userData.password || ''

      // 重置密码编辑状态
      showCurrentPassword.value = false
      userForm.newPassword = ''

      // 设置权限值
      userForm.perm_talent = userData.perm_talent || 1
      userForm.perm_team_leader = userData.perm_team_leader || 1
      userForm.perm_product = userData.perm_product || 1
      userForm.perm_order = userData.perm_order || 1
      userForm.perm_boost = userData.perm_boost || 1
      userForm.perm_performance_overview = userData.perm_performance_overview || 1
      userForm.perm_performance_statistics = userData.perm_performance_statistics || 1
      userForm.perm_sample_management = userData.perm_sample_management || 1
      userForm.perm_hide_product = userData.perm_hide_product || 0
      userForm.perm_manage_tags = userData.perm_manage_tags || 0
      userForm.perm_merchant_commission_orders = userData.perm_merchant_commission_orders || 1
      userForm.perm_reserved_service_fee_orders = userData.perm_reserved_service_fee_orders || 1
      userForm.perm_team_leader_commission_orders = userData.perm_team_leader_commission_orders || 1

      // 设置细粒度权限
      if (userData.permissions) {
        try {
          userForm.permissions =
            typeof userData.permissions === 'string'
              ? JSON.parse(userData.permissions)
              : userData.permissions
        } catch (error) {
          console.error('解析权限数据失败:', error)
          userForm.permissions = {
            talent: { data: [], operations: [] },
            team_leader: { data: [], operations: [] },
            product: { data: [], operations: [] },
            order: { data: [], operations: [], modules: [] },
            boost: { data: [], operations: [] },
            performance: { data: [], operations: [], modules: [] },
            sample: { data: [], operations: [] },
          }
        }
      } else {
        // 如果没有细粒度权限数据，使用默认值
        userForm.permissions = {
          talent: { data: [], operations: [] },
          team_leader: { data: [], operations: [] },
          product: { data: [], operations: [] },
          order: { data: [], operations: [], modules: [] },
          boost: { data: [], operations: [] },
          performance: { data: [], operations: [], modules: [] },
          sample: { data: [], operations: [] },
        }
      }

      // 重置权限展开状态
      Object.keys(expandedPermissions).forEach((key) => {
        expandedPermissions[key as keyof typeof expandedPermissions] = false
      })

      userDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请检查网络连接')
  }
}

// 处理删除用户
const handleDelete = (row: User) => {
  ElMessageBox.confirm(`确定要删除用户 ${row.name} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const response = await axios.delete(`/api/auth/business-users/${row.id}`)
        if (response.data.code === 0) {
          ElMessage.success('删除用户成功')
          getUserList()
        } else {
          ElMessage.error(response.data.message || '删除用户失败')
        }
      } catch (error: any) {
        if (error.response && error.response.data) {
          ElMessage.error(error.response.data.message || '删除用户失败')
        } else {
          ElMessage.error('删除用户失败，请检查网络连接')
        }
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 提交用户表单
const submitUserForm = async () => {
  if (!userFormRef.value) return

  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (dialogType.value === 'add') {
          // 添加用户
          const response = await axios.post('/api/auth/business-users', {
            username: userForm.username,
            name: userForm.name,
            password: userForm.password,
            role: userForm.role,
            // 权限字段
            perm_talent: userForm.perm_talent,
            perm_team_leader: userForm.perm_team_leader,
            perm_product: userForm.perm_product,
            perm_order: userForm.perm_order,
            perm_boost: userForm.perm_boost,
            perm_performance_overview: userForm.perm_performance_overview,
            perm_performance_statistics: userForm.perm_performance_statistics,
            perm_sample_management: userForm.perm_sample_management,
            perm_hide_product: userForm.perm_hide_product,
            perm_manage_tags: userForm.perm_manage_tags,
            perm_merchant_commission_orders: userForm.perm_merchant_commission_orders,
            perm_reserved_service_fee_orders: userForm.perm_reserved_service_fee_orders,
            perm_team_leader_commission_orders: userForm.perm_team_leader_commission_orders,
            permissions: userForm.permissions,
          })

          if (response.data.code === 0) {
            ElMessage.success('添加用户成功')
            userDialogVisible.value = false
            getUserList()
          } else {
            ElMessage.error(response.data.message || '添加用户失败')
          }
        } else {
          // 编辑用户
          const updateData: any = {
            name: userForm.name,
            role: userForm.role,
            // 权限字段
            perm_talent: userForm.perm_talent,
            perm_team_leader: userForm.perm_team_leader,
            perm_product: userForm.perm_product,
            perm_order: userForm.perm_order,
            perm_boost: userForm.perm_boost,
            perm_performance_overview: userForm.perm_performance_overview,
            perm_performance_statistics: userForm.perm_performance_statistics,
            perm_sample_management: userForm.perm_sample_management,
            perm_hide_product: userForm.perm_hide_product,
            perm_manage_tags: userForm.perm_manage_tags,
            perm_merchant_commission_orders: userForm.perm_merchant_commission_orders,
            perm_reserved_service_fee_orders: userForm.perm_reserved_service_fee_orders,
            perm_team_leader_commission_orders: userForm.perm_team_leader_commission_orders,
            permissions: userForm.permissions,
          }

          // 如果输入了新密码，则添加到更新数据中
          if (userForm.newPassword && userForm.newPassword.trim()) {
            updateData.password = userForm.newPassword
          }

          const response = await axios.put(`/api/auth/business-users/${userForm.id}`, updateData)

          if (response.data.code === 0) {
            ElMessage.success('更新用户成功')
            userDialogVisible.value = false
            getUserList()
          } else {
            ElMessage.error(response.data.message || '更新用户失败')
          }
        }
      } catch (error: any) {
        if (error.response && error.response.data) {
          ElMessage.error(error.response.data.message || '操作失败')
        } else {
          ElMessage.error('操作失败，请检查网络连接')
        }
      } finally {
        submitting.value = false
      }
    }
  })
}

// 权限相关方法
const togglePermissionDetail = (module: keyof typeof expandedPermissions) => {
  expandedPermissions[module] = !expandedPermissions[module]
}

const handleModulePermissionChange = (module: keyof UserPermissions, enabled: number) => {
  if (!enabled) {
    // 如果禁用模块权限，清空细粒度权限
    userForm.permissions[module].data = []
    userForm.permissions[module].operations = []
    expandedPermissions[module] = false
  } else {
    // 如果启用模块权限，设置默认权限
    setDefaultPermissions(module)
  }
}

const setDefaultPermissions = (module: keyof UserPermissions) => {
  const defaultPermissions: UserPermissions = {
    talent: {
      data: [],
      operations: ['view', 'add', 'edit', 'assign'],
    },
    team_leader: {
      data: [],
      operations: ['view', 'add', 'edit', 'assign'],
    },
    product: {
      data: [],
      operations: ['view', 'add', 'edit', 'promote'],
    },
    order: {
      data: [],
      operations: [],
      modules: [],
    },
    boost: {
      data: [],
      operations: ['view', 'add', 'edit'],
    },
    performance: {
      data: [],
      operations: [],
      modules: [],
    },
    sample: {
      data: [],
      operations: ['view', 'add', 'edit'],
    },
  }

  userForm.permissions[module] = { ...defaultPermissions[module] } as any
}

// 监听URL参数变化
onMounted(() => {
  // 从URL获取角色过滤参数
  const queryRole = route.query.role as string
  if (queryRole) {
    roleFilter.value = queryRole
  }

  getUserList()
})

// 监听角色过滤变化
watch(roleFilter, (newValue) => {
  // 更新URL参数
  const query = { ...route.query }
  if (newValue) {
    query.role = newValue
  } else {
    delete query.role
  }

  // 使用replace而不是push，避免创建新的历史记录
  router.replace({ query })

  // 重新获取用户列表
  getUserList()
})
</script>

<style scoped>
.business-users-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 编辑用户对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

:deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px;
  background-color: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

/* 表单样式优化 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

/* 权限配置面板样式 */
.permissions-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.permission-card {
  border: 1px solid #e4e7ed;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: white;
}

.permission-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.permission-card :deep(.el-card__header) {
  padding: 14px 18px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
}

.permission-card :deep(.el-card__body) {
  padding: 0;
}

.permission-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.permission-title {
  font-weight: 600;
  color: #303133;
  flex: 1;
  font-size: 15px;
}

.permission-header :deep(.el-button) {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
}

.permission-detail {
  padding: 18px;
  background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
  border-top: 1px solid #e4e7ed;
}

.permission-section {
  margin-bottom: 16px;
}

.permission-section:last-child {
  margin-bottom: 0;
}

.permission-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  padding-bottom: 6px;
  border-bottom: 2px solid #e9ecef;
  display: inline-block;
}

.permission-section :deep(.el-checkbox-group) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 8px 12px;
  margin-top: 4px;
}

.permission-section :deep(.el-checkbox) {
  margin-right: 0;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
  background-color: white;
  border: 1px solid #e4e7ed;
}

.permission-section :deep(.el-checkbox:hover) {
  background-color: #f0f8ff;
  border-color: #409eff;
}

.permission-section :deep(.el-checkbox.is-checked) {
  background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
  border-color: #409eff;
  color: white;
}

.permission-section :deep(.el-checkbox.is-checked .el-checkbox__label) {
  color: white;
}

.permission-section :deep(.el-checkbox__label) {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.permission-section :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: white;
  border-color: white;
}

.permission-section :deep(.el-checkbox__input.is-checked .el-checkbox__inner::after) {
  border-color: #409eff;
}

/* 对话框底部按钮样式 */
.dialog-footer :deep(.el-button) {
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 500;
}

.dialog-footer :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
  border: none;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .permissions-panel {
    grid-template-columns: 1fr;
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .permission-section :deep(.el-checkbox-group) {
    grid-template-columns: 1fr;
  }
}
</style>
