<template>
  <div class="talent-performance-ranking">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2 class="title">
            <el-icon class="title-icon"><TrendCharts /></el-icon>
            达人业绩排行榜
          </h2>
          <div class="header-actions">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="dateShortcuts"
              @change="handleDateChange"
              size="small"
            />
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-area">
        <el-form :inline="true" :model="searchForm" class="filter-form">
          <el-form-item label="达人昵称">
            <el-input v-model="searchForm.nickname" placeholder="请输入达人昵称" />
          </el-form-item>
          <el-form-item label="排序方式">
            <el-select
              v-model="searchForm.sortBy"
              placeholder="请选择排序方式"
              @change="handleSearch"
            >
              <el-option label="支付金额降序" value="amount_desc" />
              <el-option label="支付金额升序" value="amount_asc" />
              <el-option label="订单量降序" value="order_desc" />
              <el-option label="订单量升序" value="order_asc" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 排行榜列表 -->
      <div class="ranking-list" v-loading="loading">
        <div
          v-for="(item, index) in tableData"
          :key="item.promoter_id"
          class="ranking-item"
          :class="getRankingClass((currentPage - 1) * pageSize + index + 1)"
        >
          <!-- 排名徽章 -->
          <div
            class="rank-badge"
            :class="getRankBadgeClass((currentPage - 1) * pageSize + index + 1)"
          >
            <span v-if="(currentPage - 1) * pageSize + index + 1 <= 3" class="rank-icon">
              <el-icon v-if="(currentPage - 1) * pageSize + index + 1 === 1"><Trophy /></el-icon>
              <el-icon v-else-if="(currentPage - 1) * pageSize + index + 1 === 2"
                ><Medal
              /></el-icon>
              <el-icon v-else><Star /></el-icon>
            </span>
            <span v-else class="rank-number">{{ (currentPage - 1) * pageSize + index + 1 }}</span>
          </div>

          <!-- 达人信息 -->
          <div class="talent-info">
            <div class="talent-avatar">
              <el-avatar :size="60" :src="item.avatar_url" />
            </div>
            <div class="talent-details">
              <div class="talent-name">{{ item.nickname }}</div>
              <div class="talent-meta">
                <span class="talent-id">ID: {{ item.promoter_id }}</span>
                <span class="business-contact" v-if="item.business_contact">
                  商务: {{ item.business_contact }}
                </span>
              </div>
            </div>
          </div>

          <!-- 核心指标 -->
          <div class="metrics">
            <div class="metric-item primary">
              <div class="metric-label">支付金额</div>
              <div class="metric-value">¥{{ item.totalAmountStr }}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">订单量</div>
              <div class="metric-value">{{ item.order_count }}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">佣金收入</div>
              <div class="metric-value">¥{{ item.commissionAmountStr }}</div>
            </div>
          </div>

          <!-- 详细数据 -->
          <div class="detail-info">
            <div class="detail-section">
              <div class="section-title">业绩数据</div>
              <div class="detail-row">
                <span class="detail-label">平均订单:</span>
                <span class="detail-value"
                  >¥{{ (item.payment_amount / item.order_count || 0).toFixed(2) }}</span
                >
              </div>
              <div class="detail-row">
                <span class="detail-label">佣金率:</span>
                <span class="detail-value"
                  >{{
                    ((item.commission_amount / item.payment_amount) * 100 || 0).toFixed(2)
                  }}%</span
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="!loading && tableData.length === 0" description="暂无数据" />
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { TrendCharts, Search, RefreshLeft, Trophy, Medal, Star } from '@element-plus/icons-vue'

import { useUserStore } from '@/stores/user'

// 用户信息
const userStore = useUserStore()
// 修复获取用户角色和名称的问题
const userRole = ref(userStore.user?.is_admin ? 'admin' : 'business')
const userName = ref(userStore.user?.name || '')

// 日期范围
const dateRange = ref([new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000), new Date()])

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 7 * 24 * 60 * 60 * 1000)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 90 * 24 * 60 * 60 * 1000)
      return [start, end]
    },
  },
]

// 搜索表单
const searchForm = reactive({
  nickname: '',
  sortBy: 'amount_desc', // 默认按支付金额降序
})

// 表格数据，使用正确的类型
const tableData = ref<any[]>([])

// 分页
const currentPage = ref(1)
const pageSize = ref(12) // 卡片显示，每页12个
const total = ref(0)
const loading = ref(false)

// 当前用户信息
const currentUser = ref<any>(null)

// 用户权限
const userPermissions = ref<any>({
  performance: {
    data: [],
    operations: [],
    modules: [],
  },
})

// 权限检查方法
const hasModulePermission = (permission: string) => {
  return userPermissions.value.performance.modules.includes(permission)
}

// 便捷权限检查 - 达人业绩统计通过perm_performance_statistics控制显示隐藏
const canAccessTalentPerformanceStatistics = computed(() =>
  hasModulePermission('performance_statistics'),
)

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.nickname = ''
  searchForm.sortBy = 'amount_desc'
  handleSearch()
}

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)

      // 解析权限信息
      if (currentUser.value.permissions) {
        try {
          const permissions =
            typeof currentUser.value.permissions === 'string'
              ? JSON.parse(currentUser.value.permissions)
              : currentUser.value.permissions
          userPermissions.value = permissions
        } catch (permError) {
          console.error('解析权限信息失败:', permError)
          // 使用默认权限
          userPermissions.value = {
            performance: { data: [], operations: [], modules: [] },
          }
        }
      }

      console.log('当前登录用户:', currentUser.value)
      console.log('业绩模块权限:', userPermissions.value.performance)
    } else {
      console.error('未找到登录用户信息')
      ElMessage.warning('未找到登录用户信息，请重新登录')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请重新登录')
  }
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 构建请求参数
    const params: any = {
      page: currentPage.value,
      pageSize: pageSize.value,
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = formatDate(dateRange.value[0])
      params.endDate = formatDate(dateRange.value[1])
    }

    if (searchForm.nickname) {
      params.nickname = searchForm.nickname
    }

    if (searchForm.sortBy) {
      params.sortBy = searchForm.sortBy
    }

    // 获取达人业绩统计数据
    const token = localStorage.getItem('token')
    const response = await axios.get('/api/performance/talent-statistics', {
      params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (response.data.code === 200) {
      // 更新表格数据
      tableData.value = response.data.data.list || []
      total.value = response.data.data.total || 0
    } else {
      ElMessage.error(response.data.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取业绩统计数据失败:', error)
    ElMessage.error('获取业绩统计数据失败')
  } finally {
    loading.value = false
  }
}

// 日期变化
const handleDateChange = () => {
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchData()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchData()
}

// 获取排名样式类
const getRankingClass = (rank: number) => {
  if (rank <= 3) return 'top-three'
  if (rank <= 10) return 'top-ten'
  return ''
}

// 获取排名徽章样式类
const getRankBadgeClass = (rank: number) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  return 'normal'
}

onMounted(() => {
  getCurrentUser()
  fetchData()
})
</script>

<style scoped>
.talent-performance-ranking {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-icon {
  margin-right: 8px;
  font-size: 28px;
  color: #ff6b6b;
}

.filter-area {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.2);
}

.filter-form {
  margin: 0;
}

.filter-form :deep(.el-form-item__label) {
  color: white;
  font-weight: 500;
}

.ranking-list {
  min-height: 400px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 16px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ranking-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff6b6b, #feca57);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ranking-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

.ranking-item:hover::before {
  opacity: 1;
}

.ranking-item.top-three {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border: 2px solid #fdcb6e;
}

.ranking-item.top-ten {
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8c0 100%);
  border: 2px solid #00b894;
}

.pagination-container {
  margin-top: 24px;
  text-align: center;
}

.stat-value.order-count {
  color: #409eff;
}

.stat-value.amount {
  color: #67c23a;
  font-size: 24px;
}

/* 排名徽章样式 */
.rank-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 18px;
  font-weight: bold;
  margin-right: 20px;
  flex-shrink: 0;
}

.rank-badge.gold {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b4513;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
}

.rank-badge.silver {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #555;
  box-shadow: 0 4px 20px rgba(192, 192, 192, 0.4);
}

.rank-badge.bronze {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: white;
  box-shadow: 0 4px 20px rgba(205, 127, 50, 0.4);
}

.rank-badge.normal {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  box-shadow: 0 4px 20px rgba(116, 185, 255, 0.3);
}

.rank-icon {
  font-size: 24px;
}

.rank-number {
  font-size: 20px;
}

/* 达人信息样式 */
.talent-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 20px;
}

.talent-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #feca57);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.talent-details {
  flex: 1;
}

.talent-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.talent-id {
  font-size: 12px;
  color: #ff6b6b;
  background: #ffe6e6;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.business-contact {
  font-size: 12px;
  color: #4a5568;
  font-weight: 500;
}

/* 核心指标样式 */
.metrics {
  display: flex;
  gap: 24px;
  margin-right: 20px;
}

.metric-item {
  text-align: center;
  min-width: 100px;
}

.metric-item.primary .metric-value {
  color: #e53e3e;
  font-weight: bold;
  font-size: 18px;
}

.metric-label {
  font-size: 12px;
  color: #718096;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  font-family: 'Courier New', monospace;
}

/* 详细信息样式 */
.detail-info {
  min-width: 200px;
}

.detail-section {
  flex: 1;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
  text-align: center;
  padding: 4px 8px;
  background: #f7fafc;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 11px;
}

.detail-label {
  color: #718096;
}

.detail-value {
  font-weight: 600;
  color: #2d3748;
  font-family: 'Courier New', monospace;
}

.total-info {
  color: #909399;
  font-size: 14px;
}
</style>
