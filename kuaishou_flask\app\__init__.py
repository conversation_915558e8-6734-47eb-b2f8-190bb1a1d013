from flask import Flask
from flask_cors import CORS
from apscheduler.schedulers.background import BackgroundScheduler
from .routes.home import home_bp

def create_app():
    app = Flask(__name__)
    app.config.from_object('app.config.config')
    
    # 允许跨域请求
    CORS(app)
    
    # 注册蓝图
    from app.routes.talent import talent_bp
    from app.routes.product import product_bp
    from app.routes.activity import activity_bp
    from app.routes.order import order_bp
    from app.routes.auth import auth_bp
    from app.routes.team_leader import team_leader_bp
    from app.routes.tags import tags_bp
    from app.routes.system import system_bp
    from app.routes.performance import performance_bp
    from app.routes.sync_management import sync_management_bp
    from app.routes.boost import boost_bp
    app.register_blueprint(talent_bp)
    app.register_blueprint(product_bp)
    app.register_blueprint(activity_bp)
    app.register_blueprint(order_bp)
    app.register_blueprint(auth_bp)
    app.register_blueprint(team_leader_bp)
    app.register_blueprint(tags_bp)
    app.register_blueprint(system_bp)
    app.register_blueprint(home_bp, url_prefix='/api/home')
    app.register_blueprint(performance_bp, url_prefix='/api/performance')
    app.register_blueprint(sync_management_bp, url_prefix='/api/sync')
    app.register_blueprint(boost_bp, url_prefix='/api/boost')


    # # 启动新的业绩统计同步服务
    # try:
    #     from app.services.performance_sync_service import performance_sync_service
    #     performance_sync_service.start()
    #     print("📊 新版业绩统计同步服务已启动")
    # except Exception as e:
    #     print(f"启动业绩统计同步服务失败: {str(e)}")

    # # 启动达人商务关联同步服务
    # try:
    #     from app.services.talent_business_sync_service import talent_business_sync_service
    #     talent_business_sync_service.start()
    #     print("🔗 达人商务关联同步服务已启动")
    # except Exception as e:
    #     print(f"启动达人商务关联同步服务失败: {str(e)}")

    # # 启动自动同步服务
    # try:
    #     from app.services.complete_auto_sync_service import complete_auto_sync_service
    #     complete_auto_sync_service.start()
    #     print("完整版自动同步服务已启动")
    # except Exception as e:
    #     print(f"启动自动同步服务失败: {str(e)}")

    return app