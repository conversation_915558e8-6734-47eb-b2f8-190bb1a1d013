<template>
  <div class="home-container">
    <!-- 欢迎卡片 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="welcome-card" shadow="hover">
          <div class="welcome-header">
            <div class="welcome-info">
              <el-avatar :size="64" :src="userInfo.avatar || '/assets/default-avatar.png'" />
              <div class="welcome-text">
                <h2>欢迎回来，{{ userInfo.name }}</h2>
                <p>{{ currentTime }} · {{ userInfo.team || '电商团队' }}</p>
              </div>
            </div>
            <div class="welcome-stats">
              <div class="stat-item">
                <div class="stat-value">{{ personalStats.orderCount }}</div>
                <div class="stat-label">本月订单量</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">¥{{ personalStats.gmv }}</div>
                <div class="stat-label">本月GMV</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">¥{{ personalStats.serviceFee }}</div>
                <div class="stat-label">本月服务费</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="quick-actions" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>快捷操作</h3>
            </div>
          </template>
          <div class="action-buttons">
            <el-button type="primary" @click="navigateTo('/product/list')">
              <el-icon><Plus /></el-icon>添加推广
            </el-button>
            <el-button type="success" @click="navigateTo('/sample/management')">
              <el-icon><Box /></el-icon>寄样管理
            </el-button>
            <el-button type="warning" @click="navigateTo('/performance/overview')">
              <el-icon><DataAnalysis /></el-icon>业绩概览
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排行榜区域 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="8">
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>达人排行榜</h3>
              <el-radio-group
                v-model="timeRange.talent"
                size="small"
                @change="handleTalentTimeRangeChange"
              >
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div v-if="loading.talent" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else-if="rankings.talent.length === 0" class="empty-data">暂无数据</div>
          <div v-else class="ranking-list">
            <div
              v-for="(item, index) in rankings.talent"
              :key="item.id"
              class="ranking-item"
              :class="{ 'top-three': index < 3 }"
            >
              <div class="rank-number" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
              <div class="rank-info">
                <div class="rank-name">{{ item.name }}</div>
                <div class="rank-value">¥{{ item.amount }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>爆品排行榜</h3>
              <el-radio-group
                v-model="timeRange.product"
                size="small"
                @change="handleProductTimeRangeChange"
              >
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div v-if="loading.product" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else-if="rankings.product.length === 0" class="empty-data">暂无数据</div>
          <div v-else class="ranking-list">
            <div
              v-for="(item, index) in rankings.product"
              :key="item.id"
              class="ranking-item product-item"
              :class="{ 'top-three': index < 3 }"
            >
              <div class="rank-number" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
              <div class="rank-info">
                <div class="rank-name" :title="item.name">{{ item.name }}</div>
                <div class="rank-stats">
                  <div class="rank-sales">销量: {{ item.sales }}件</div>
                  <div class="rank-value">GMV: ¥{{ item.amount }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>商务排行榜</h3>
              <el-radio-group
                v-model="timeRange.business"
                size="small"
                @change="handleBusinessTimeRangeChange"
              >
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div v-if="loading.business" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else-if="rankings.business.length === 0" class="empty-data">暂无数据</div>
          <div v-else class="ranking-list">
            <div
              v-for="(item, index) in rankings.business"
              :key="item.id"
              class="ranking-item"
              :class="{ 'top-three': index < 3, 'current-user': item.name === userInfo.name }"
            >
              <div class="rank-number" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
              <div class="rank-info">
                <div class="rank-name">{{ item.name }}</div>
                <div class="rank-value">¥{{ item.amount }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据统计和公告区域 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="16">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>业绩趋势</h3>
              <el-radio-group v-model="chartType" size="small">
                <el-radio-button label="gmv">GMV</el-radio-button>
                <el-radio-button label="serviceFee">服务费</el-radio-button>
                <el-radio-button label="orderCount">订单量</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="chartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="notice-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>公告栏</h3>
            </div>
          </template>
          <div class="notice-content">
            <div v-for="(notice, index) in notices" :key="index" class="notice-item">
              <div class="notice-title">
                <el-icon><Bell /></el-icon>
                <span>{{ notice.title }}</span>
              </div>
              <div class="notice-text">{{ notice.content }}</div>
              <div class="notice-time">{{ notice.time }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import * as echarts from 'echarts'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { Plus, Box, DataAnalysis, Bell } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => {
  // 使用类型断言来解决 TypeScript 错误
  const user = (userStore.user as any) || {}
  return {
    name: user.name || '用户',
    avatar: localStorage.getItem('userAvatar') || '/assets/default-avatar.png',
    role: localStorage.getItem('userRole') || 'business',
    team: '电商团队',
  }
})

// 当前时间
const currentTime = ref(formatDate(new Date()))
function formatDate(date: Date) {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hours = date.getHours()
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[date.getDay()]
  return `${year}年${month}月${day}日 ${hours}:${minutes} ${weekday}`
}

// 个人统计数据
const personalStats = reactive({
  orderCount: 0,
  gmv: '0.00',
  serviceFee: '0.00',
})

// 添加以下接口定义
interface RankingItem {
  id: string
  name: string
  amount: string
}

interface ProductRankingItem {
  id: string
  name: string
  amount: string
  sales: string
}

// 修改 rankings 的定义
const rankings = reactive({
  talent: [] as RankingItem[],
  product: [] as ProductRankingItem[],
  business: [] as RankingItem[],
})

// 修改 chartData 和 seriesData 类型定义
const chartData = reactive({
  dates: [] as string[],
  gmv: [] as number[],
  serviceFee: [] as number[],
  orderCount: [] as number[],
})

// 时间范围状态
const timeRange = reactive({
  talent: 'month',
  product: 'month',
  business: 'month',
})

// 加载状态
const loading = reactive({
  talent: true,
  product: true,
  business: true,
  chart: true,
})

// 图表相关
const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null
const chartType = ref('gmv')

// 公告数据
const notices = ref([
  {
    title: '系统更新通知',
    content: '系统将于2025年7月31日进行版本更新，届时将新增更多功能，敬请期待。',
    time: '2025-07-23',
  },
  {
    title: '业务流程变更',
    content: '自8月1日起，所有寄样申请需提前3个工作日提交，请各位商务注意安排时间。',
    time: '2025-07-22',
  },
])

// 页面跳转
const navigateTo = (path: string) => {
  router.push(path)
}

// 时间范围切换处理函数
const handleTalentTimeRangeChange = (range: string) => {
  fetchTalentRanking(range)
}

const handleProductTimeRangeChange = (range: string) => {
  fetchProductRanking(range)
}

const handleBusinessTimeRangeChange = (range: string) => {
  fetchBusinessRanking(range)
}

// 获取个人统计数据
const fetchPersonalStats = async () => {
  try {
    const token = localStorage.getItem('token')
    const currentMonth = new Date().toISOString().slice(0, 7) // 格式: YYYY-MM

    const response = await axios.get('/api/home/<USER>', {
      params: { month: currentMonth },
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      const data = response.data.data
      personalStats.orderCount = data.orderCount || 0
      personalStats.gmv = data.gmv || '0.00'
      personalStats.serviceFee = data.serviceFee || '0.00'
    }
  } catch (error) {
    console.error('获取个人统计数据失败:', error)
    // 使用模拟数据
    personalStats.orderCount = 128
    personalStats.gmv = '25680.00'
    personalStats.serviceFee = '2568.00'
  }
}

// 获取达人排行榜
const fetchTalentRanking = async (range = 'month') => {
  loading.talent = true
  try {
    const token = localStorage.getItem('token')
    const params: any = { limit: 10, range }

    if (range === 'month') {
      params.month = new Date().toISOString().slice(0, 7) // 格式: YYYY-MM
    }

    const response = await axios.get('/api/home/<USER>', {
      params,
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      rankings.talent = response.data.data || []
    }
  } catch (error) {
    console.error('获取达人排行榜失败:', error)
    // 使用模拟数据
    rankings.talent = [
      { id: '1', name: '达人A', amount: '15680.00' },
      { id: '2', name: '达人B', amount: '12450.00' },
      { id: '3', name: '达人C', amount: '9870.00' },
      { id: '4', name: '达人D', amount: '7650.00' },
      { id: '5', name: '达人E', amount: '5430.00' },
    ]
  } finally {
    loading.talent = false
  }
}

// 获取爆品排行榜
const fetchProductRanking = async (range = 'month') => {
  loading.product = true
  try {
    const token = localStorage.getItem('token')
    const params: any = { limit: 10, range }

    if (range === 'month') {
      params.month = new Date().toISOString().slice(0, 7) // 格式: YYYY-MM
    }

    const response = await axios.get('/api/home/<USER>', {
      params,
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      rankings.product = response.data.data || []
    }
  } catch (error) {
    console.error('获取爆品排行榜失败:', error)
    // 使用模拟数据
    rankings.product = [
      { id: '1', name: '爆款商品A', amount: '125680.00', sales: '1256' },
      { id: '2', name: '热销商品B', amount: '98450.00', sales: '984' },
      { id: '3', name: '推荐商品C', amount: '76320.00', sales: '763' },
      { id: '4', name: '精选商品D', amount: '54210.00', sales: '542' },
      { id: '5', name: '优质商品E', amount: '43180.00', sales: '431' },
    ]
  } finally {
    loading.product = false
  }
}

// 获取商务排行榜
const fetchBusinessRanking = async (range = 'month') => {
  loading.business = true
  try {
    const token = localStorage.getItem('token')
    const params: any = { limit: 10, range }

    if (range === 'month') {
      params.month = new Date().toISOString().slice(0, 7) // 格式: YYYY-MM
    }

    const response = await axios.get('/api/home/<USER>', {
      params,
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      rankings.business = response.data.data || []
    }
  } catch (error) {
    console.error('获取商务排行榜失败:', error)
    // 使用模拟数据
    rankings.business = [
      { id: '1', name: '商务A', amount: '25680.00' },
      { id: '2', name: userInfo.value.name, amount: '23450.00' },
      { id: '3', name: '商务C', amount: '19870.00' },
      { id: '4', name: '商务D', amount: '17650.00' },
      { id: '5', name: '商务E', amount: '15430.00' },
    ]
  } finally {
    loading.business = false
  }
}

// 获取图表数据
const fetchChartData = async () => {
  loading.chart = true
  try {
    const token = localStorage.getItem('token')
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30)

    const response = await axios.get('/api/home/<USER>', {
      params: {
        startDate: startDate.toISOString().slice(0, 10),
        endDate: endDate.toISOString().slice(0, 10),
      },
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      const data = response.data.data || []
      chartData.dates = data.map((item: any) => item.date)
      chartData.gmv = data.map((item: any) => item.gmv)
      chartData.serviceFee = data.map((item: any) => item.serviceFee)
      chartData.orderCount = data.map((item: any) => item.orderCount)

      initChart()
    }
  } catch (error) {
    console.error('获取图表数据失败:', error)
    // 使用模拟数据
    const dates = []
    const gmv = []
    const serviceFee = []
    const orderCount = []

    const endDate = new Date()
    for (let i = 14; i >= 0; i--) {
      const date = new Date()
      date.setDate(endDate.getDate() - i)
      dates.push(date.toISOString().slice(5, 10))
      gmv.push(Math.floor(Math.random() * 5000 + 5000))
      serviceFee.push(Math.floor(Math.random() * 500 + 500))
      orderCount.push(Math.floor(Math.random() * 20 + 10))
    }

    chartData.dates = dates
    chartData.gmv = gmv
    chartData.serviceFee = serviceFee
    chartData.orderCount = orderCount

    initChart()
  } finally {
    loading.chart = false
  }
}

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    updateChart()

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', handleResize)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  let seriesData: number[] = []
  let yAxisName = ''

  switch (chartType.value) {
    case 'gmv':
      seriesData = chartData.gmv
      yAxisName = 'GMV (元)'
      break
    case 'serviceFee':
      seriesData = chartData.serviceFee
      yAxisName = '服务费 (元)'
      break
    case 'orderCount':
      seriesData = chartData.orderCount
      yAxisName = '订单量'
      break
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        const data = params[0]
        const value = chartType.value === 'orderCount' ? data.value : `¥${data.value.toFixed(2)}`
        return `${data.name}<br/>${data.seriesName}: ${value}`
      },
    },
    xAxis: {
      type: 'category',
      data: chartData.dates,
      axisLabel: {
        interval: Math.floor(chartData.dates.length / 7),
      },
    },
    yAxis: {
      type: 'value',
      name: yAxisName,
      axisLabel: {
        formatter: function (value: number) {
          return chartType.value === 'orderCount' ? value : `¥${value}`
        },
      },
    },
    series: [
      {
        name:
          chartType.value === 'gmv'
            ? 'GMV'
            : chartType.value === 'serviceFee'
              ? '服务费'
              : '订单量',
        type: 'line',
        data: seriesData,
        smooth: true,
        lineStyle: {
          width: 3,
          color:
            chartType.value === 'gmv'
              ? '#409EFF'
              : chartType.value === 'serviceFee'
                ? '#67C23A'
                : '#E6A23C',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color:
                chartType.value === 'gmv'
                  ? 'rgba(64, 158, 255, 0.7)'
                  : chartType.value === 'serviceFee'
                    ? 'rgba(103, 194, 58, 0.7)'
                    : 'rgba(230, 162, 60, 0.7)',
            },
            {
              offset: 1,
              color:
                chartType.value === 'gmv'
                  ? 'rgba(64, 158, 255, 0.1)'
                  : chartType.value === 'serviceFee'
                    ? 'rgba(103, 194, 58, 0.1)'
                    : 'rgba(230, 162, 60, 0.1)',
            },
          ]),
        },
      },
    ],
  }

  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  chartInstance?.resize()
}

// 监听图表类型变化
watch(chartType, () => {
  updateChart()
})

// 定时更新当前时间
let timeInterval: number | null = null

onMounted(() => {
  fetchPersonalStats()
  fetchTalentRanking(timeRange.talent)
  fetchProductRanking(timeRange.product)
  fetchBusinessRanking(timeRange.business)
  fetchChartData()

  // 每分钟更新一次时间
  timeInterval = setInterval(() => {
    currentTime.value = formatDate(new Date())
  }, 60000) as unknown as number
})

onUnmounted(() => {
  // 清除定时器
  if (timeInterval !== null) {
    clearInterval(timeInterval)
  }

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)

  // 销毁图表实例
  chartInstance?.dispose()
})
</script>

<style scoped>
.home-container {
  padding: 20px;
  height: 100%;
  width: 100%;
  overflow: auto;
  box-sizing: border-box;
}

.mt-20 {
  margin-top: 20px;
}

/* 欢迎卡片样式 */
.welcome-card {
  height: 100%;
}

.welcome-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.welcome-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.welcome-text h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.welcome-text p {
  margin: 8px 0 0;
  color: #909399;
  font-size: 14px;
}

.welcome-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.stat-item {
  text-align: center;
  padding: 10px 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 快捷操作卡片样式 */
.quick-actions {
  height: 100%;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-buttons .el-button {
  width: 100%;
  justify-content: flex-start;
  padding: 15px;
  font-size: 16px;
}

.action-buttons .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 排行榜卡片样式 */
.ranking-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.loading-container,
.empty-data {
  padding: 20px 0;
  text-align: center;
  color: #909399;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.ranking-item:hover {
  transform: translateX(5px);
  background-color: #ecf5ff;
}

.ranking-item.top-three {
  background-color: #ecf5ff;
}

.ranking-item.current-user {
  background-color: #f0f9eb;
  border-left: 4px solid #67c23a;
}

.rank-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #909399;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-right: 12px;
}

.rank-1 {
  background-color: #f56c6c;
}

.rank-2 {
  background-color: #e6a23c;
}

.rank-3 {
  background-color: #409eff;
}

.rank-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rank-name {
  font-weight: 500;
  color: #303133;
}

.rank-value {
  font-weight: bold;
  color: #409eff;
}

/* 图表卡片样式 */
.chart-card {
  height: 100%;
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 公告卡片样式 */
.notice-card {
  height: 100%;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.notice-item {
  padding: 10px;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.notice-item:hover {
  background-color: #ecf5ff;
}

.notice-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
}

.notice-text {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 8px;
}

.notice-time {
  text-align: right;
  color: #909399;
  font-size: 12px;
}

/* 爆品排行榜样式 */
.product-item .rank-info {
  flex: 1;
  min-width: 0;
}

.product-item .rank-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-item .rank-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-item .rank-sales {
  font-size: 12px;
  color: #67c23a;
  font-weight: 500;
}

.product-item .rank-value {
  font-size: 12px;
  color: #409eff;
  font-weight: 600;
}
</style>
