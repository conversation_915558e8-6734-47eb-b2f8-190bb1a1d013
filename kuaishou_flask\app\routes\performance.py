"""
业绩统计和概览相关路由
"""
import logging
import os
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
import jwt
from app.utils.db_utils import get_connection

# 创建蓝图
performance_bp = Blueprint('performance', __name__)

def format_datetime(dt):
    """格式化日期时间"""
    if dt is None:
        return None
    if isinstance(dt, str):
        return dt
    return dt.strftime('%Y-%m-%d %H:%M:%S')

@performance_bp.route('/overview', methods=['GET'])
def get_performance_overview():
    """获取业绩概览数据（基于商务业绩表）"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌进行权限验证
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            # 验证令牌
            SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_role = payload.get('role', '')
            business_name = payload.get('name', '')
        except Exception as e:
            return jsonify({
                'code': 401,
                'message': f'令牌验证失败: {str(e)}',
                'data': None
            }), 401

        # 获取请求参数
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')

        if not start_date or not end_date:
            # 默认查询最近30天的数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件（基于day字段进行日期范围查询）
        conditions = ["day BETWEEN %s AND %s"]
        params = [start_date, end_date]

        # 权限控制：商务用户只能查看自己的数据
        if user_role == 'business':
            conditions.append("business_name = %s")
            params.append(business_name)

        where_clause = " AND ".join(conditions)

        # 查询当前时间段的统计数据（从商务业绩表）
        stats_query = f"""
            SELECT
                SUM(income_order_count) as income_orders,
                SUM(expense_order_count) as expense_orders,
                SUM(income_payment_amount) as income_amount,
                SUM(expense_payment_amount) as expense_amount,
                SUM(income_estimated_service_fee) as income_service_fee,
                SUM(expense_estimated_service_fee) as expense_service_fee,
                SUM(income_settled_service_fee) as income_settled_fee,
                SUM(expense_settled_service_fee) as expense_settled_fee,
                SUM(net_estimated_service_fee) as net_service_fee,
                SUM(net_settled_service_fee) as net_settled_fee
            FROM business_performance
            WHERE {where_clause}
        """

        cursor.execute(stats_query, params)
        current_stats = cursor.fetchone()

        # 计算上一时间段的日期范围（用于环比）
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        date_diff = (end_date_obj - start_date_obj).days + 1

        prev_end_date = start_date_obj - timedelta(days=1)
        prev_start_date = start_date_obj - timedelta(days=date_diff)

        prev_start_str = prev_start_date.strftime('%Y-%m-%d')
        prev_end_str = prev_end_date.strftime('%Y-%m-%d')

        prev_params = [prev_start_str, prev_end_str]
        if user_role == 'business':
            prev_params.append(business_name)

        cursor.execute(stats_query, prev_params)
        prev_stats = cursor.fetchone()

        # 计算环比变化率
        def calculate_change(current, previous):
            if not previous or previous == 0:
                return 0
            return round((current - previous) / previous * 100, 2)

        # 查询每日数据（用于图表显示）
        daily_query = f"""
            SELECT
                day as date,
                SUM(income_payment_amount) as income_gmv,
                SUM(expense_payment_amount) as expense_gmv,
                SUM(income_payment_amount + expense_payment_amount) as total_gmv,
                SUM(income_estimated_service_fee) as income_service_fee,
                SUM(expense_estimated_service_fee) as expense_service_fee,
                SUM(net_estimated_service_fee) as net_service_fee,
                SUM(income_order_count) as income_orders,
                SUM(expense_order_count) as expense_orders,
                SUM(income_order_count + expense_order_count) as total_orders
            FROM business_performance
            WHERE {where_clause}
            GROUP BY day
            ORDER BY day
        """

        cursor.execute(daily_query, params)
        daily_data = cursor.fetchall()

        # 格式化每日数据
        for item in daily_data:
            item['date'] = item['date'].strftime('%Y-%m-%d')
            item['income_gmv'] = float(item['income_gmv'] or 0)
            item['expense_gmv'] = float(item['expense_gmv'] or 0)
            item['total_gmv'] = float(item['total_gmv'] or 0)
            item['income_service_fee'] = float(item['income_service_fee'] or 0)
            item['expense_service_fee'] = float(item['expense_service_fee'] or 0)
            item['net_service_fee'] = float(item['net_service_fee'] or 0)

        # 准备返回数据
        statistics = {
            'totalGmv': f"{float(current_stats['income_amount'] or 0) + float(current_stats['expense_amount'] or 0):.2f}",
            'totalGmvCompare': calculate_change(
                float(current_stats['income_amount'] or 0) + float(current_stats['expense_amount'] or 0),
                float(prev_stats['income_amount'] or 0) + float(prev_stats['expense_amount'] or 0)
            ),
            'incomeGmv': f"{float(current_stats['income_amount'] or 0):.2f}",
            'incomeGmvCompare': calculate_change(
                float(current_stats['income_amount'] or 0),
                float(prev_stats['income_amount'] or 0)
            ),
            'expenseGmv': f"{float(current_stats['expense_amount'] or 0):.2f}",
            'expenseGmvCompare': calculate_change(
                float(current_stats['expense_amount'] or 0),
                float(prev_stats['expense_amount'] or 0)
            ),
            'netServiceFee': f"{float(current_stats['net_service_fee'] or 0):.2f}",
            'netServiceFeeCompare': calculate_change(
                float(current_stats['net_service_fee'] or 0),
                float(prev_stats['net_service_fee'] or 0)
            ),
            'totalOrders': (current_stats['income_orders'] or 0) + (current_stats['expense_orders'] or 0),
            'totalOrdersCompare': calculate_change(
                (current_stats['income_orders'] or 0) + (current_stats['expense_orders'] or 0),
                (prev_stats['income_orders'] or 0) + (prev_stats['expense_orders'] or 0)
            ),
            'incomeOrders': current_stats['income_orders'] or 0,
            'incomeOrdersCompare': calculate_change(
                current_stats['income_orders'] or 0,
                prev_stats['income_orders'] or 0
            ),
            'expenseOrders': current_stats['expense_orders'] or 0,
            'expenseOrdersCompare': calculate_change(
                current_stats['expense_orders'] or 0,
                prev_stats['expense_orders'] or 0
            )
        }

        return jsonify({
            'code': 200,
            'message': '获取业绩概览数据成功',
            'data': {
                'statistics': statistics,
                'dailyData': daily_data
            }
        })

    except Exception as e:
        logging.error(f"获取业绩概览数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取业绩概览数据失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@performance_bp.route('/statistics', methods=['GET'])
def get_performance_statistics():
    """获取业绩统计数据（基于商务业绩表）"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌进行权限验证
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            # 验证令牌
            SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_role = payload.get('role', '')
            business_name = payload.get('name', '')
        except Exception as e:
            return jsonify({
                'code': 401,
                'message': f'令牌验证失败: {str(e)}',
                'data': None
            }), 401

        # 获取请求参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        business_search = request.args.get('businessName')  # 改为商务名称搜索
        sort_by = request.args.get('sortBy', 'net_estimated_desc')  # 排序方式

        if not start_date or not end_date:
            # 默认查询最近30天的数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件（基于day字段进行日期范围查询）
        conditions = ["day BETWEEN %s AND %s"]
        params = [start_date, end_date]

        # 权限控制：商务用户只能查看自己的数据
        if user_role == 'business':
            conditions.append("business_name = %s")
            params.append(business_name)

        if business_search:
            conditions.append("business_name LIKE %s")
            params.append(f"%{business_search}%")

        where_clause = " AND ".join(conditions)

        # 构建排序子句
        order_mapping = {
            'net_estimated_desc': 'SUM(net_estimated_service_fee) DESC',
            'net_estimated_asc': 'SUM(net_estimated_service_fee) ASC',
            'net_settled_desc': 'SUM(net_settled_service_fee) DESC',
            'net_settled_asc': 'SUM(net_settled_service_fee) ASC',
            'total_amount_desc': 'SUM(income_payment_amount + expense_payment_amount) DESC',
            'total_amount_asc': 'SUM(income_payment_amount + expense_payment_amount) ASC'
        }
        order_clause = order_mapping.get(sort_by, 'SUM(net_estimated_service_fee) DESC')

        # 查询商务业绩统计（按商务聚合多天的数据，关联用户表获取头像）
        stats_query = f"""
            SELECT
                bp.business_name,
                bu.avatar,
                SUM(bp.income_order_count) as income_orders,
                SUM(bp.expense_order_count) as expense_orders,
                SUM(bp.income_order_count + bp.expense_order_count) as total_orders,
                SUM(bp.income_payment_amount) as income_amount,
                SUM(bp.expense_payment_amount) as expense_amount,
                SUM(bp.income_payment_amount + bp.expense_payment_amount) as total_amount,
                SUM(bp.income_estimated_service_fee) as income_service_fee,
                SUM(bp.expense_estimated_service_fee) as expense_service_fee,
                SUM(bp.net_estimated_service_fee) as net_service_fee,
                SUM(bp.income_settled_service_fee) as income_settled_fee,
                SUM(bp.expense_settled_service_fee) as expense_settled_fee,
                SUM(bp.net_settled_service_fee) as net_settled_fee,
                AVG((bp.income_payment_amount + bp.expense_payment_amount) / NULLIF(bp.income_order_count + bp.expense_order_count, 0)) as avg_order_amount
            FROM business_performance bp
            LEFT JOIN business_user bu ON bp.business_name = bu.name
            WHERE {where_clause.replace('business_name', 'bp.business_name')}
            GROUP BY bp.business_name, bu.avatar
            ORDER BY {order_clause.replace('SUM(', 'SUM(bp.')}
        """

        cursor.execute(stats_query, params)
        all_data = cursor.fetchall()

        # 计算总数
        total = len(all_data)

        # 分页处理
        offset = (page - 1) * page_size
        paged_data = all_data[offset:offset + page_size]

        # 格式化数据
        for item in paged_data:
            item['income_orders'] = item['income_orders'] or 0
            item['expense_orders'] = item['expense_orders'] or 0
            item['total_orders'] = item['total_orders'] or 0
            item['income_amount'] = float(item['income_amount'] or 0)
            item['expense_amount'] = float(item['expense_amount'] or 0)
            item['total_amount'] = float(item['total_amount'] or 0)
            item['income_service_fee'] = float(item['income_service_fee'] or 0)
            item['expense_service_fee'] = float(item['expense_service_fee'] or 0)
            item['net_service_fee'] = float(item['net_service_fee'] or 0)
            item['income_settled_fee'] = float(item['income_settled_fee'] or 0)
            item['expense_settled_fee'] = float(item['expense_settled_fee'] or 0)
            item['net_settled_fee'] = float(item['net_settled_fee'] or 0)
            item['avg_order_amount'] = float(item['avg_order_amount'] or 0)

            # 处理头像字段
            item['avatar'] = item.get('avatar') or '/assets/default-avatar.png'

            # 添加格式化的字符串字段
            item['totalAmountStr'] = f"{item['total_amount']:.2f}"
            item['incomeAmountStr'] = f"{item['income_amount']:.2f}"
            item['expenseAmountStr'] = f"{item['expense_amount']:.2f}"
            item['incomeServiceFeeStr'] = f"{item['income_service_fee']:.2f}"
            item['expenseServiceFeeStr'] = f"{item['expense_service_fee']:.2f}"
            item['netServiceFeeStr'] = f"{item['net_service_fee']:.2f}"
            item['incomeSettledFeeStr'] = f"{item['income_settled_fee']:.2f}"
            item['expenseSettledFeeStr'] = f"{item['expense_settled_fee']:.2f}"
            item['netSettledFeeStr'] = f"{item['net_settled_fee']:.2f}"
            item['avgOrderAmountStr'] = f"{item['avg_order_amount']:.2f}"

        return jsonify({
            'code': 200,
            'message': '获取商务业绩统计数据成功',
            'data': {
                'list': paged_data,
                'total': total,
                'page': page,
                'pageSize': page_size
            }
        })

    except Exception as e:
        logging.error(f"获取商务业绩统计数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取商务业绩统计数据失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@performance_bp.route('/talent-statistics', methods=['GET'])
def get_talent_performance_statistics():
    """获取达人业绩统计数据（基于达人业绩表）"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌进行权限验证
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            # 验证令牌
            SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_role = payload.get('role', '')
            business_name = payload.get('name', '')
        except Exception as e:
            return jsonify({
                'code': 401,
                'message': f'令牌验证失败: {str(e)}',
                'data': None
            }), 401

        # 获取请求参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        nickname = request.args.get('nickname')
        team = request.args.get('team')
        sort_by = request.args.get('sortBy', 'amount_desc')  # 排序方式

        if not start_date or not end_date:
            # 默认查询最近30天的数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件（基于day字段进行日期范围查询）
        conditions = ["day BETWEEN %s AND %s", "promoter_type = 1"]  # 只查询达人
        params = [start_date, end_date]

        # 权限控制：商务用户只能查看自己的达人数据
        if user_role == 'business':
            # 通过talent表的business_contact字段和talent_name匹配promoter_performance表的promoter_name
            # 获取该商务负责的达人列表
            talent_query = """
            SELECT talent_name
            FROM talent
            WHERE business_contact = %s
               OR (talent_category = 'shared' AND FIND_IN_SET(%s, shared_businesses) > 0)
            """
            cursor.execute(talent_query, (business_name, business_name))
            talent_results = cursor.fetchall()

            if talent_results:
                # 提取达人名称列表
                talent_names = [talent['talent_name'] for talent in talent_results]
                # 构建IN条件
                placeholders = ','.join(['%s'] * len(talent_names))
                conditions.append(f"promoter_name IN ({placeholders})")
                params.extend(talent_names)
            else:
                # 如果没有找到任何达人，返回空结果
                conditions.append("1 = 0")  # 永远为false的条件
        elif team:
            # 管理员和运营可以按团队筛选
            conditions.append("business_contact = %s")
            params.append(team)

        if nickname:
            conditions.append("promoter_name LIKE %s")
            params.append(f"%{nickname}%")

        where_clause = " AND ".join(conditions)

        # 构建排序子句
        order_mapping = {
            'amount_desc': 'SUM(payment_amount) DESC',
            'amount_asc': 'SUM(payment_amount) ASC',
            'order_desc': 'SUM(order_count) DESC',
            'order_asc': 'SUM(order_count) ASC'
        }
        order_clause = order_mapping.get(sort_by, 'SUM(payment_amount) DESC')

        # 查询达人业绩统计（按达人聚合多天的数据）
        stats_query = f"""
            SELECT
                promoter_id,
                promoter_name as nickname,
                business_contact as team,
                SUM(order_count) as order_count,
                SUM(payment_amount) as total_amount,
                SUM(commission_amount) as commission_amount,
                AVG(payment_amount / NULLIF(order_count, 0)) as avg_order_amount
            FROM promoter_performance
            WHERE {where_clause}
            GROUP BY promoter_id, promoter_name, business_contact
            ORDER BY {order_clause}
        """

        cursor.execute(stats_query, params)
        all_data = cursor.fetchall()

        # 计算总数
        total = len(all_data)

        # 分页处理
        offset = (page - 1) * page_size
        paged_data = all_data[offset:offset + page_size]

        # 格式化数据
        for item in paged_data:
            item['total_amount'] = float(item['total_amount'] or 0)
            item['commission_amount'] = float(item['commission_amount'] or 0)
            item['avg_order_amount'] = float(item['avg_order_amount'] or 0)

            # 计算服务费（假设平均服务费率为3%）
            item['service_fee'] = item['total_amount'] * 0.03

            # 添加格式化的字符串字段
            item['totalAmountStr'] = f"{item['total_amount']:.2f}"
            item['serviceFeeStr'] = f"{item['service_fee']:.2f}"
            item['commissionAmountStr'] = f"{item['commission_amount']:.2f}"
            item['avgOrderAmountStr'] = f"{item['avg_order_amount']:.2f}"

        return jsonify({
            'code': 200,
            'message': '获取达人业绩统计数据成功',
            'data': {
                'list': paged_data,
                'total': total,
                'page': page,
                'pageSize': page_size
            }
        })

    except Exception as e:
        logging.error(f"获取达人业绩统计数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取达人业绩统计数据失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@performance_bp.route('/detail/<nickname>', methods=['GET'])
def get_performance_detail(nickname):
    """获取达人业绩详情（基于达人业绩表）"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌进行权限验证
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            # 验证令牌
            SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_role = payload.get('role', '')
            business_name = payload.get('name', '')
        except Exception as e:
            return jsonify({
                'code': 401,
                'message': f'令牌验证失败: {str(e)}',
                'data': None
            }), 401

        # 获取请求参数
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')

        if not start_date or not end_date:
            # 默认查询最近30天的数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

        # 转换为月份范围
        start_month = start_date[:7]  # YYYY-MM
        end_month = end_date[:7]      # YYYY-MM

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件
        conditions = [
            "month BETWEEN %s AND %s",
            "promoter_type = 1",  # 只查询达人
            "promoter_name = %s"
        ]
        params = [start_month, end_month, nickname]

        # 权限控制：商务用户只能查看自己的达人数据
        if user_role == 'business':
            conditions.append("business_contact = %s")
            params.append(business_name)

        where_clause = " AND ".join(conditions)

        # 查询趋势数据（按月份分组）
        trend_query = f"""
            SELECT
                month as date,
                SUM(payment_amount) as gmv,
                SUM(commission_amount) as income,
                SUM(payment_amount * 0.03) as expense
            FROM promoter_performance
            WHERE {where_clause}
            GROUP BY month
            ORDER BY month
        """

        cursor.execute(trend_query, params)
        trend_data = cursor.fetchall()

        # 格式化趋势数据
        for item in trend_data:
            item['gmv'] = float(item['gmv'] or 0)
            item['income'] = float(item['income'] or 0)
            item['expense'] = float(item['expense'] or 0)

        # 查询达人基本信息
        talent_query = f"""
            SELECT
                promoter_id,
                promoter_name,
                business_contact,
                SUM(order_count) as total_orders,
                SUM(payment_amount) as total_gmv,
                SUM(payment_amount * 0.03) as total_service_fee
            FROM promoter_performance
            WHERE {where_clause}
            GROUP BY promoter_id, promoter_name, business_contact
        """

        cursor.execute(talent_query, params)
        talent_info = cursor.fetchone()

        talent_data = [{
            'id': talent_info['promoter_id'] if talent_info else 'T001',
            'nickname': nickname,
            'type': '达人',
            'fans': '10.5万',  # 模拟数据
            'orderCount': talent_info['total_orders'] if talent_info else 0,
            'gmv': f"{float(talent_info['total_gmv'] or 0):.2f}" if talent_info else "0.00",
            'serviceFee': f"{float(talent_info['total_service_fee'] or 0):.2f}" if talent_info else "0.00"
        }]

        # 查询最近的订单数据（从订单表获取，限制数量）
        order_query = f"""
            SELECT
                o.order_id as id,
                o.product_name as productName,
                o.payment_amount as price,
                CASE
                    WHEN o.order_status = 30 THEN '已付款'
                    WHEN o.order_status = 50 THEN '已收货'
                    WHEN o.order_status = 60 THEN '已结算'
                    WHEN o.order_status = 80 THEN '已失效'
                    ELSE '未知'
                END as status,
                (o.payment_amount * 0.03) as serviceFee,
                o.order_time as createTime
            FROM `order` o
            WHERE o.promoter_name = %s
            AND o.promotion_type = 1
            AND o.order_time BETWEEN %s AND %s
            {"AND o.talent_business = %s" if user_role == 'business' else ""}
            ORDER BY o.order_time DESC
            LIMIT 100
        """

        order_params = [nickname, f"{start_date} 00:00:00", f"{end_date} 23:59:59"]
        if user_role == 'business':
            order_params.append(business_name)

        cursor.execute(order_query, order_params)
        order_data = cursor.fetchall()

        # 格式化订单数据
        for item in order_data:
            item['price'] = f"{float(item['price'] or 0):.2f}"
            item['serviceFee'] = f"{float(item['serviceFee'] or 0):.2f}"
            item['createTime'] = format_datetime(item['createTime'])

        return jsonify({
            'code': 200,
            'message': '获取业绩详情成功',
            'data': {
                'trendData': trend_data,
                'talentData': talent_data,
                'orderData': order_data
            }
        })

    except Exception as e:
        logging.error(f"获取业绩详情失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取业绩详情失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
