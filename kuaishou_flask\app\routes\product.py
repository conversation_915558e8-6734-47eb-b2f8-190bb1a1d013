from flask import Blueprint, request, jsonify
from app.utils.db_utils import get_connection
from app.utils.config_utils import get_kuaishou_cookie
import sys
import os
import logging
import jwt
from datetime import datetime
from app.routes.auth import admin_required

# 用于JWT签名的密钥
SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')

# 导入爬虫模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../python')))
try:
    from activity_item import fetch_activity_items, get_all_activity_items
    from activity_list import fetch_activity_list
    from promotion_manager import PromotionManager
    logging.info("成功导入快手商品爬虫模块和推广管理模块")
except ImportError as e:
    logging.error(f"导入快手商品爬虫模块失败: {str(e)}")
    fetch_activity_items = None
    get_all_activity_items = None
    fetch_activity_list = None
    PromotionManager = None

product_bp = Blueprint('product', __name__, url_prefix='/api/product')

def get_product_status_text(status):
    """
    根据状态码返回状态文本
    1: 在售
    5: 已下架
    其他: 未知
    """
    status_map = {
        1: '在售',
        5: '已下架'
    }
    return status_map.get(status, '未知')

# 快手商品爬虫包装类
class KuaishouProductCrawler:
    """快手商品爬虫类，用于从快手后台获取商品数据"""
    
    def __init__(self):
        # 从数据库获取cookie配置
        self.cookie = get_kuaishou_cookie()
    
    def get_activity_list(self, activity_type=1, activity_status=3, page=1, page_size=10):
        """
        获取活动列表
        
        Args:
            activity_type: 活动类型，默认1
            activity_status: 活动状态，默认3
            page: 页码，默认1
            page_size: 每页数量，默认10
            
        Returns:
            dict: 包含活动列表和总数
        """
        if fetch_activity_list is not None:
            try:
                result = fetch_activity_list(
                    cookie=self.cookie,
                    activity_type=activity_type,
                    activity_status=activity_status,
                    page=page,
                    page_size=page_size
                )
                
                if "error" in result:
                    logging.error(f"获取活动列表失败: {result['error']}")
                    return {
                        "activities": [],
                        "total": 0
                    }
                
                return result
            except Exception as e:
                logging.error(f"获取活动列表失败: {str(e)}")
                return {
                    "activities": [],
                    "total": 0
                }
        else:
            logging.error("活动列表爬虫模块未导入，无法获取活动列表")
            return {
                "activities": [],
                "total": 0
            }
    
    def get_activity_items(self, activity_id, page=1, page_size=10):
        """
        获取活动商品列表
        
        Args:
            activity_id: 活动ID
            page: 页码，默认1
            page_size: 每页数量，默认10
            
        Returns:
            dict: 包含商品列表和总数
        """
        if fetch_activity_items is not None:
            try:
                result = fetch_activity_items(
                    cookie=self.cookie,
                    activity_id=activity_id,
                    current_page=page,
                    page_size=page_size
                )
                
                if "error" in result:
                    logging.error(f"获取商品列表失败: {result['error']}")
                    return {
                        "products": [],
                        "total": 0
                    }
                
                # 处理商品数据
                items = result.get("items", [])
                products = [self._format_product(item) for item in items]
                
                return {
                    "products": products,
                    "total": result.get("total", 0),
                    "page": page,
                    "page_size": page_size
                }
            except Exception as e:
                logging.error(f"获取商品列表失败: {str(e)}")
                return {
                    "products": [],
                    "total": 0
                }
        else:
            logging.error("商品爬虫模块未导入，无法获取商品列表")
            return {
                "products": [],
                "total": 0
            }
    
    def get_all_activity_items(self, activity_id, max_pages=None):
        """
        获取指定活动的所有商品
        
        Args:
            activity_id: 活动ID
            max_pages: 最大页数，None表示获取所有
            
        Returns:
            list: 所有商品数据
        """
        if get_all_activity_items is not None:
            try:
                all_items = get_all_activity_items(
                    cookie=self.cookie,
                    activity_id=activity_id,
                    max_pages=max_pages
                )
                
                # 处理商品数据
                products = [self._format_product(item) for item in all_items]
                return products
            except Exception as e:
                logging.error(f"获取所有商品数据失败: {str(e)}")
                return []
        else:
            logging.error("商品爬虫模块未导入，无法获取所有商品数据")
            return []
    
    def get_product_detail(self, product_id, activity_id=None):
        """
        获取商品详情
        
        Args:
            product_id: 商品ID
            activity_id: 活动ID，如果提供则从指定活动中查找
            
        Returns:
            dict: 商品详情
        """
        # 如果提供了活动ID，则从指定活动中查找商品
        if activity_id and get_all_activity_items is not None:
            try:
                # 获取该活动的所有商品
                all_items = get_all_activity_items(self.cookie, activity_id)
                
                # 查找指定ID的商品
                for item in all_items:
                    if str(item.get('itemId', '')) == str(product_id):
                        return self._format_product(item)
            except Exception as e:
                logging.error(f"从活动中获取商品详情失败: {str(e)}")
        
        # 如果没有找到或者出错，返回模拟数据
        return {
            'id': product_id,
            'name': f'商品_{product_id}',
            'price': 99.90,
            'merchant_commission': '20%',
            'talent_commission': '10%',
            'merchant_id': '12345',
            'merchant_name': '测试商家',
            'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'image_url': 'https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item-default.jpg',
            'category': '测试分类',
            'stock': 1000,
            'sales': 100,
            'status': 1,
            'store_score': '4.8'
        }
    
    def _format_product(self, item):
        """
        格式化商品数据
        
        Args:
            item: 原始商品数据
            
        Returns:
            dict: 格式化后的商品数据
        """
        # 将价格从分转换为元显示
        price = item.get('itemPrice', 0) / 100 if 'itemPrice' in item else 0
        
        # 创建时间
        create_time = datetime.fromtimestamp(item.get('itemApplyTime', 0)/1000).strftime('%Y-%m-%d %H:%M:%S') if 'itemApplyTime' in item else ''
        
        return {
            'id': str(item.get('itemId', '')),
            'name': item.get('itemTitle', ''),
            'price': price,
            'merchant_commission': item.get('itemCommissionRateView', '0%'),
            'talent_commission': item.get('investmentPromotionRateView', '0%'),  # 保持前端兼容性
            'investment_commission': item.get('investmentPromotionRateView', '0%'),
            'merchant_id': str(item.get('sellerId', '')),
            'merchant_name': item.get('sellerNickName', ''),
            'create_time': create_time,
            'image_url': item.get('itemImgUrl', ''),
            'category': item.get('itemCategoryName', ''),
            'stock': item.get('itemStock', 0),
            'sales': item.get('itemVolume', 0),
            'status': item.get('itemStatus', 0),
            'store_score': item.get('storeScore', ''),
            'activity_id': str(item.get('activityId', '')),
            'control_commission_score': None,
            'cooperation_score': None,
            'trust_score': None,
            'cost_performance_score': None,
            'routine_score': None,
            'assist_broadcast_score': None,
            'remark': None,
            'tag': None
        }

@product_bp.route('/list', methods=['GET'])
def get_product_list():
    """获取商品列表，支持分页和搜索"""
    connection = None
    cursor = None
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 10, type=int)
        name = request.args.get('name', '')
        product_id = request.args.get('id', '')
        merchant_id = request.args.get('merchant_id', '')
        merchant_name = request.args.get('merchant_name', '')
        min_price = request.args.get('min_price', '')
        max_price = request.args.get('max_price', '')
        category = request.args.get('category', '')
        tag = request.args.get('tag', '')
        status = request.args.get('status', '')
        hidden_status = request.args.get('hidden_status', '')  # 隐藏状态过滤
        custom_tags = request.args.get('custom_tags', '')  # 自定义标签过滤，多个标签用逗号分隔
        activity_id = request.args.get('activity_id', 9369212614, type=int)
        sort_by = request.args.get('sort_by', 'id')  # 排序字段
        sort_order = request.args.get('sort_order', 'desc')  # 排序方向
        
        # 尝试从数据库获取数据
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 检查用户权限，决定是否显示隐藏商品
        user_role = 'business'  # 默认角色
        can_see_hidden = False

        # 尝试获取用户权限
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            try:
                token = auth_header.split(' ')[1]
                payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
                user_role = payload.get('role', 'business')
                user_id = payload.get('user_id')

                # 管理员可以看到所有商品
                if user_role == 'admin':
                    can_see_hidden = True
                elif user_role == 'operation':
                    # 检查运营用户是否有隐藏商品权限
                    perm_query = "SELECT perm_hide_product FROM business_user WHERE id = %s"
                    cursor.execute(perm_query, (user_id,))
                    perm_result = cursor.fetchone()
                    if perm_result and perm_result['perm_hide_product'] == 1:
                        can_see_hidden = True
            except:
                # 权限验证失败，使用默认权限
                pass

        # 构建查询条件
        params = []
        where_clause = "1=1"

        # 根据权限和隐藏状态过滤
        if hidden_status == 'hidden':
            # 只显示隐藏商品（需要权限）
            if can_see_hidden:
                where_clause += " AND (is_hidden = 1 OR is_hidden IS NULL)"
            else:
                # 无权限查看隐藏商品，返回空结果
                where_clause += " AND 1=0"
        elif hidden_status == 'visible':
            # 只显示可见商品
            where_clause += " AND (is_hidden = 0 OR is_hidden IS NULL)"
        else:
            # 默认情况：根据权限决定是否显示隐藏商品
            if not can_see_hidden:
                where_clause += " AND (is_hidden = 0 OR is_hidden IS NULL)"
        
        if name:
            where_clause += " AND product_name LIKE %s"
            params.append(f"%{name}%")
            # 添加调试：查询数据库中是否有包含该关键词的商品
            debug_query = "SELECT product_id, product_name FROM product WHERE product_name LIKE %s LIMIT 5"
            cursor.execute(debug_query, [f"%{name}%"])
            debug_results = cursor.fetchall()
            logging.info(f"调试查询结果: {debug_results}")
        
        if product_id:
            # 去除商品ID前后的空格
            product_id = product_id.strip()
            where_clause += " AND product_id = %s"
            params.append(product_id)
            # 添加调试：查询数据库中是否有该商品ID
            debug_query = "SELECT product_id, product_name FROM product WHERE product_id = %s"
            cursor.execute(debug_query, [product_id])
            debug_result = cursor.fetchone()
            logging.info(f"商品ID调试查询结果: {debug_result}")
            
        if merchant_id:
            # 去除商家ID前后的空格
            merchant_id = merchant_id.strip()
            where_clause += " AND merchant_id = %s"
            params.append(merchant_id)
            
        if merchant_name:
            where_clause += " AND merchant_name LIKE %s"
            params.append(f"%{merchant_name}%")
            
        if min_price:
            where_clause += " AND price >= %s"
            params.append(float(min_price))
            
        if max_price:
            where_clause += " AND price <= %s"
            params.append(float(max_price))
            
        if category:
            where_clause += " AND category LIKE %s"
            params.append(f"%{category}%")
            
        if tag:
            where_clause += " AND tag = %s"
            params.append(tag)
            
        if status:
            where_clause += " AND status = %s"
            params.append(int(status))

        # 自定义标签过滤
        if custom_tags:
            tag_names = [tag.strip() for tag in custom_tags.split(',') if tag.strip()]
            if tag_names:
                # 使用子查询来过滤有指定标签的商品
                tag_placeholders = ','.join(['%s'] * len(tag_names))
                where_clause += f"""
                AND product_id IN (
                    SELECT DISTINCT r.product_id
                    FROM product_tag_relations r
                    INNER JOIN product_tags t ON r.tag_id = t.id
                    WHERE t.name IN ({tag_placeholders})
                )
                """
                params.extend(tag_names)

        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM product WHERE {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 如果数据库中有数据，则从数据库获取
        formatted_products = []
        
        if total > 0:
            # 构建排序语句
            valid_sort_fields = ['id', 'product_id', 'product_name', 'price', 'create_time', 'stock', 'sales', 'status']
            if sort_by not in valid_sort_fields:
                sort_by = 'id'
            
            if sort_order.upper() not in ['ASC', 'DESC']:
                sort_order = 'DESC'
            
            # 查询数据
            query = f"SELECT * FROM product WHERE {where_clause} ORDER BY {sort_by} {sort_order} LIMIT %s OFFSET %s"
            params.extend([page_size, (page - 1) * page_size])
            cursor.execute(query, params)
            products = cursor.fetchall()
            
            # 格式化数据
            for p in products:
                # 获取商品的自定义标签
                tag_query = """
                SELECT t.id, t.name, t.color
                FROM product_tags t
                INNER JOIN product_tag_relations r ON t.id = r.tag_id
                WHERE r.product_id = %s
                ORDER BY t.name
                """
                cursor.execute(tag_query, (p['product_id'],))
                custom_tags_data = cursor.fetchall()

                formatted_products.append({
                    'id': p['product_id'],
                    'name': p['product_name'],
                    'price': float(p['price']),  # 直接使用数据表中的价格
                    'merchant_id': p.get('merchant_id', ''),
                    'merchant_name': p.get('merchant_name', ''),
                    'merchant_commission': p.get('merchant_commission', '0%'),
                    'talent_commission': p.get('investment_commission', '0%'),  # 使用新字段
                    'investment_commission': p.get('investment_commission', '0%'),
                    'create_time': p['create_time'].strftime('%Y-%m-%d %H:%M:%S') if p.get('create_time') else '',
                    'image_url': p.get('product_image_url', ''),
                    'category': p.get('category', ''),
                    'stock': p.get('stock', 0),
                    'sales': p.get('sales', 0),
                    'status': p.get('status', 1),
                    'status_text': get_product_status_text(p.get('status', 1)),
                    'store_score': round(float(p.get('store_score', 0)), 2) if p.get('store_score') else 0,
                    'activity_id': p.get('activity_id', ''),
                    'control_commission_score': round(float(p.get('control_commission_score', 0)), 2) if p.get('control_commission_score') else 0,
                    'cooperation_score': round(float(p.get('cooperation_score', 0)), 2) if p.get('cooperation_score') else 0,
                    'trust_score': round(float(p.get('trust_score', 0)), 2) if p.get('trust_score') else 0,
                    'cost_performance_score': round(float(p.get('cost_performance_score', 0)), 2) if p.get('cost_performance_score') else 0,
                    'routine_score': round(float(p.get('routine_score', 0)), 2) if p.get('routine_score') else 0,
                    'assist_broadcast_score': round(float(p.get('assist_broadcast_score', 0)), 2) if p.get('assist_broadcast_score') else 0,
                    'remark': p.get('remark'),
                    'tag': p.get('tag'),
                    'is_hidden': p.get('is_hidden', 0),  # 添加隐藏状态
                    'custom_tags': custom_tags_data,  # 添加自定义标签
                    # 添加新字段
                    'business_contact': p.get('business_contact', ''),
                    'operation_contact': p.get('operation_contact', ''),
                    'merchant_commission_rate': p.get('merchant_commission_rate', 0),
                    'team_leader_commission_rate': p.get('team_leader_commission_rate', 0),
                    'service_fee_rate': p.get('service_fee_rate', 0),
                    'reserved_service_fee_rate': p.get('reserved_service_fee_rate', 0)
                })
        else:
            logging.info("数据库中没有匹配数据，返回空列表")
        
        return jsonify({
            'code': 0,
            'message': 'success',
            'data': {
                'products': formatted_products,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        })
    except Exception as e:
        logging.error(f"获取商品列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取商品列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/detail/<product_id>', methods=['GET'])
def get_product_detail(product_id):
    """获取商品详情"""
    connection = None
    cursor = None
    try:
        # 获取活动ID参数
        activity_id = request.args.get('activity_id')
        
        # 尝试从数据库获取数据
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询商品
        query = "SELECT * FROM product WHERE product_id = %s"
        cursor.execute(query, [product_id])
        product = cursor.fetchone()
        
        if product:
            # 格式化数据
            formatted_product = {
                'id': product['product_id'],
                'name': product['product_name'],
                'price': float(product['price']),  # 直接使用价格，不进行转换
                'merchant_id': product.get('merchant_id', ''),
                'merchant_name': product.get('merchant_name', ''),
                'merchant_commission': product.get('merchant_commission', '0%'),
                'talent_commission': product.get('investment_commission', '0%'),  # 使用新字段
                'investment_commission': product.get('investment_commission', '0%'),
                'create_time': product['create_time'].strftime('%Y-%m-%d %H:%M:%S') if product.get('create_time') else '',
                'image_url': product.get('product_image_url', ''),
                'category': product.get('category', ''),
                'stock': product.get('stock', 0),
                'sales': product.get('sales', 0),
                'status': product.get('status', 1),
                'store_score': product.get('store_score', ''),
                'activity_id': product.get('activity_id', ''),
                'control_commission_score': product.get('control_commission_score'),
                'cooperation_score': product.get('cooperation_score'),
                'trust_score': product.get('trust_score'),
                'cost_performance_score': product.get('cost_performance_score'),
                'routine_score': product.get('routine_score'),
                'assist_broadcast_score': product.get('assist_broadcast_score'),
                'remark': product.get('remark'),
                'tag': product.get('tag'),
                # 添加新字段
                'business_contact': product.get('business_contact', ''),
                'operation_contact': product.get('operation_contact', ''),
                'merchant_commission_rate': product.get('merchant_commission_rate', 0),
                'team_leader_commission_rate': product.get('team_leader_commission_rate', 0),
                'service_fee_rate': product.get('service_fee_rate', 0),
                'reserved_service_fee_rate': product.get('reserved_service_fee_rate', 0)
            }
        else:
            # 如果数据库中没有，则使用爬虫获取
            crawler = KuaishouProductCrawler()
            formatted_product = crawler.get_product_detail(product_id, activity_id)
        
        return jsonify({
            'code': 0,
            'message': 'success',
            'data': formatted_product
        })
    except Exception as e:
        logging.error(f"获取商品详情失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取商品详情失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            
@product_bp.route('/sync', methods=['POST'])
def sync_products():
    """同步商品数据，从快手API获取最新数据并保存到数据库"""
    connection = None
    cursor = None
    try:
        # 获取请求参数
        data = request.json or {}
        activity_ids = data.get('activity_ids', [])  # 支持多个活动ID
        activity_id = data.get('activity_id')  # 保持向后兼容
        max_pages = data.get('max_pages')  # 最大页数，None表示获取所有
        
        # 创建爬虫实例
        crawler = KuaishouProductCrawler()
        
        # 处理活动ID列表
        if activity_ids:
            # 使用传入的多个活动ID
            activity_id_list = activity_ids
        elif activity_id:
            # 单个活动ID（向后兼容）
            activity_id_list = [activity_id]
        else:
            # 如果没有提供活动ID，则获取活动列表
            activity_result = crawler.get_activity_list(page=1, page_size=10)
            activities = activity_result.get('activities', [])
            
            if not activities:
                return jsonify({
                    'code': 400,
                    'message': '未获取到活动列表数据',
                    'data': None
                }), 400
            
            # 使用第一个活动的ID
            first_activity_id = activities[0].get('activityId')
            
            if not first_activity_id:
                return jsonify({
                    'code': 400,
                    'message': '未找到有效的活动ID',
                    'data': None
                }), 400
            
            activity_id_list = [first_activity_id]
            logging.info(f"自动选择活动ID: {first_activity_id}")
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 统计结果
        total_products = 0
        success_count = 0
        update_count = 0
        failed_count = 0
        activity_results = []
        
        # 遍历每个活动ID，获取商品数据
        for activity_id in activity_id_list:
            try:
                logging.info(f"开始同步活动ID: {activity_id}")
                
                # 使用爬虫获取该活动的所有商品数据
                products = crawler.get_all_activity_items(activity_id, max_pages)
                
                if not products:
                    logging.warning(f"活动ID {activity_id} 未获取到商品数据")
                    activity_results.append({
                        'activity_id': activity_id,
                        'status': 'no_data',
                        'message': '未获取到商品数据'
                    })
                    continue
                
                activity_success = 0
                activity_update = 0
                activity_failed = 0
                
                # 批量添加或更新商品
                for product in products:
                    try:
                        # 检查商品是否已存在
                        check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
                        cursor.execute(check_query, [product['id']])
                        result = cursor.fetchone()
                        
                        if result and result['count'] > 0:
                            # 商品已存在，更新（保留用户手动编辑的investment_commission，但更新service_fee_rate）
                            item_status = product.get('status', 1)

                            if item_status == 1:
                                # 在售状态，不更新is_hidden字段，但更新service_fee_rate
                                update_query = """
                                UPDATE product SET
                                    product_name = %s,
                                    price = %s,
                                    product_image_url = %s,
                                    activity_id = %s,
                                    merchant_id = %s,
                                    merchant_name = %s,
                                    merchant_commission = %s,
                                    service_fee_rate = %s,
                                    category = %s,
                                    stock = %s,
                                    sales = %s,
                                    status = %s,
                                    store_score = %s,
                                    update_time = NOW()
                                WHERE product_id = %s
                                """

                                cursor.execute(update_query, [
                                    product['name'],
                                    float(product['price']),  # 确保是浮点数
                                    product['image_url'],
                                    product['activity_id'],
                                    product.get('merchant_id', ''),
                                    product.get('merchant_name', ''),
                                    product.get('merchant_commission', '0%'),
                                    product.get('investment_commission', '0%'),  # 备份服务费率，总是更新
                                    product.get('category', ''),
                                    product.get('stock', 0),
                                    product.get('sales', 0),
                                    item_status,
                                    product.get('store_score', ''),
                                    product['id']
                                ])
                            else:
                                # 非在售状态，更新is_hidden为1（隐藏），同时更新service_fee_rate
                                update_query = """
                                UPDATE product SET
                                    product_name = %s,
                                    price = %s,
                                    product_image_url = %s,
                                    activity_id = %s,
                                    merchant_id = %s,
                                    merchant_name = %s,
                                    merchant_commission = %s,
                                    service_fee_rate = %s,
                                    category = %s,
                                    stock = %s,
                                    sales = %s,
                                    status = %s,
                                    store_score = %s,
                                    is_hidden = 1,
                                    update_time = NOW()
                                WHERE product_id = %s
                                """

                                cursor.execute(update_query, [
                                    product['name'],
                                    float(product['price']),  # 确保是浮点数
                                    product['image_url'],
                                    product['activity_id'],
                                    product.get('merchant_id', ''),
                                    product.get('merchant_name', ''),
                                    product.get('merchant_commission', '0%'),
                                    product.get('investment_commission', '0%'),  # 备份服务费率，总是更新
                                    product.get('category', ''),
                                    product.get('stock', 0),
                                    product.get('sales', 0),
                                    item_status,
                                    product.get('store_score', ''),
                                    product['id']
                                ])
                            activity_update += 1
                            update_count += 1
                        else:
                            # 商品不存在，插入（包含service_fee_rate备份字段）
                            insert_query = """
                            INSERT INTO product (
                                product_id, product_name, price,
                                product_image_url, activity_id, merchant_id,
                                merchant_name, merchant_commission, investment_commission,
                                service_fee_rate, category, stock, sales, status, store_score, create_time
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                            """

                            cursor.execute(insert_query, [
                                product['id'],
                                product['name'],
                                float(product['price']),  # 确保是浮点数
                                product['image_url'],
                                product['activity_id'],
                                product.get('merchant_id', ''),
                                product.get('merchant_name', ''),
                                product.get('merchant_commission', '0%'),
                                product.get('investment_commission', '0%'),  # 投资佣金字段
                                product.get('investment_commission', '0%'),  # 备份服务费率，与investment_commission相同
                                product.get('category', ''),
                                product.get('stock', 0),
                                product.get('sales', 0),
                                product.get('status', 1),
                                product.get('store_score', '')
                            ])
                            activity_success += 1
                            success_count += 1
                    except Exception as e:
                        logging.error(f"同步商品失败: {str(e)}, 商品ID: {product.get('id', 'unknown')}")
                        activity_failed += 1
                        failed_count += 1
                
                total_products += len(products)
                
                # 记录该活动的结果
                activity_results.append({
                    'activity_id': activity_id,
                    'status': 'success',
                    'total': len(products),
                    'success': activity_success,
                    'update': activity_update,
                    'failed': activity_failed
                })
                
                logging.info(f"活动ID {activity_id} 同步完成: 新增{activity_success}条，更新{activity_update}条，失败{activity_failed}条")
                
            except Exception as e:
                logging.error(f"同步活动ID {activity_id} 失败: {str(e)}")
                activity_results.append({
                    'activity_id': activity_id,
                    'status': 'error',
                    'message': str(e)
                })
                failed_count += 1
        
        # 提交事务
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '同步商品数据成功',
            'data': {
                'total': total_products,
                'success_count': success_count,
                'update_count': update_count,
                'failed_count': failed_count,
                'activity_count': len(activity_id_list),
                'activity_results': activity_results
            }
        })
    except Exception as e:
        logging.error(f"同步商品数据失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'同步商品数据失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/add', methods=['POST'])
def add_product():
    """添加商品"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空',
                'data': None
            }), 400
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查商品ID是否已存在
        check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
        cursor.execute(check_query, [data.get('product_id')])
        result = cursor.fetchone()
        if result and result['count'] > 0:
            return jsonify({
                'code': 400,
                'message': f"商品ID已存在: {data.get('product_id')}",
                'data': None
            }), 400
        
        # 准备插入数据
        fields = [
            'product_name', 'product_id', 'price', 'merchant_id', 'merchant_name',
            'merchant_commission', 'investment_commission', 'product_image_url',
            'category', 'stock', 'sales', 'status', 'store_score', 'activity_id'
        ]
        
        # 构建SQL语句
        present_fields = [field for field in fields if field in data and data[field] is not None]
        placeholders = ', '.join(['%s'] * len(present_fields))
        field_str = ', '.join(present_fields)
        
        query = f"INSERT INTO product ({field_str}) VALUES ({placeholders})"
        params = [data[field] for field in present_fields]
        
        # 执行插入
        cursor.execute(query, params)
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '添加商品成功',
            'data': {
                'product_id': data.get('product_id')
            }
        })
    except Exception as e:
        logging.error(f"添加商品失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'添加商品失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/update/<product_id>', methods=['PUT'])
def update_product(product_id):
    """更新商品"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空',
                'data': None
            }), 400
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查商品是否存在
        check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
        cursor.execute(check_query, [product_id])
        result = cursor.fetchone()
        if not result or result['count'] == 0:
            return jsonify({
                'code': 404,
                'message': f"商品不存在: {product_id}",
                'data': None
            }), 404
        
        # 准备更新数据
        fields = [
            'product_name', 'price', 'merchant_id', 'merchant_name',
            'merchant_commission', 'investment_commission', 'product_image_url',
            'category', 'stock', 'sales', 'status', 'store_score', 'activity_id'
        ]
        
        # 构建SQL语句
        update_parts = []
        params = []
        
        for field in fields:
            if field in data and data[field] is not None:
                update_parts.append(f"{field} = %s")
                params.append(data[field])
        
        if not update_parts:
            return jsonify({
                'code': 400,
                'message': '没有需要更新的字段',
                'data': None
            }), 400
        
        query = f"UPDATE product SET {', '.join(update_parts)} WHERE product_id = %s"
        params.append(product_id)
        
        # 执行更新
        cursor.execute(query, params)
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '更新商品成功',
            'data': {
                'product_id': product_id
            }
        })
    except Exception as e:
        logging.error(f"更新商品失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新商品失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/delete/<product_id>', methods=['DELETE'])
def delete_product(product_id):
    """删除商品"""
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查商品是否存在
        check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
        cursor.execute(check_query, [product_id])
        result = cursor.fetchone()
        if not result or result['count'] == 0:
            return jsonify({
                'code': 404,
                'message': f"商品不存在: {product_id}",
                'data': None
            }), 404
        
        # 执行删除
        query = "DELETE FROM product WHERE product_id = %s"
        cursor.execute(query, [product_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '删除商品成功',
            'data': {
                'product_id': product_id
            }
        })
    except Exception as e:
        logging.error(f"删除商品失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'删除商品失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/update-sales-amount', methods=['POST'])
@admin_required
def update_sales_amount():
    """更新商品销售额（价格 * 销量）"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor()

        # 更新所有商品的销售额
        update_query = """
        UPDATE product
        SET sales_amount = price * sales
        WHERE price > 0 AND sales > 0
        """
        cursor.execute(update_query)

        # 获取更新的行数
        updated_count = cursor.rowcount
        connection.commit()

        return jsonify({
            'code': 0,
            'message': f'成功更新 {updated_count} 个商品的销售额',
            'data': {
                'updated_count': updated_count
            }
        })

    except Exception as e:
        if connection:
            connection.rollback()
        logging.error(f"更新商品销售额失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'更新商品销售额失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/batch-update-scores', methods=['PUT'])
def batch_update_product_scores():
    """批量更新商品评分"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data or 'product_ids' not in data or 'scores' not in data:
            return jsonify({
                'code': 400,
                'message': '商品ID列表和评分数据不能为空',
                'data': None
            }), 400

        product_ids = data['product_ids']
        scores = data['scores']
        sync_by_name = data.get('sync_by_name', False)

        if not product_ids or len(product_ids) == 0:
            return jsonify({
                'code': 400,
                'message': '商品ID列表不能为空',
                'data': None
            }), 400

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建更新字段
        update_fields = []
        params = []

        if scores.get('control_commission_score', 0) > 0:
            update_fields.append('control_commission_score = %s')
            params.append(scores['control_commission_score'])

        if scores.get('cooperation_score', 0) > 0:
            update_fields.append('cooperation_score = %s')
            params.append(scores['cooperation_score'])

        if scores.get('trust_score', 0) > 0:
            update_fields.append('trust_score = %s')
            params.append(scores['trust_score'])

        if scores.get('cost_performance_score', 0) > 0:
            update_fields.append('cost_performance_score = %s')
            params.append(scores['cost_performance_score'])

        if scores.get('routine_score', 0) > 0:
            update_fields.append('routine_score = %s')
            params.append(scores['routine_score'])

        if scores.get('assist_broadcast_score', 0) > 0:
            update_fields.append('assist_broadcast_score = %s')
            params.append(scores['assist_broadcast_score'])

        if not update_fields:
            return jsonify({
                'code': 400,
                'message': '请至少设置一个评分',
                'data': None
            }), 400

        update_fields.append('update_time = NOW()')

        if sync_by_name:
            # 按商品名称同步：先获取选中商品的名称
            placeholders = ', '.join(['%s'] * len(product_ids))
            name_query = f"SELECT DISTINCT product_name FROM product WHERE product_id IN ({placeholders})"
            cursor.execute(name_query, product_ids)
            product_names = [row['product_name'] for row in cursor.fetchall()]

            if product_names:
                # 更新所有同名商品
                name_placeholders = ', '.join(['%s'] * len(product_names))
                query = f"UPDATE product SET {', '.join(update_fields)} WHERE product_name IN ({name_placeholders})"
                cursor.execute(query, params + product_names)
        else:
            # 只更新选中的商品
            placeholders = ', '.join(['%s'] * len(product_ids))
            query = f"UPDATE product SET {', '.join(update_fields)} WHERE product_id IN ({placeholders})"
            cursor.execute(query, params + product_ids)

        affected_rows = cursor.rowcount
        connection.commit()

        return jsonify({
            'code': 0,
            'message': f'批量更新商品评分成功，共更新{affected_rows}条记录',
            'data': {
                'affected_count': affected_rows,
                'sync_by_name': sync_by_name
            }
        })

    except Exception as e:
        logging.error(f"批量更新商品评分失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'批量更新商品评分失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/hot-ranking', methods=['GET'])
def get_hot_ranking():
    """获取爆品排行榜"""
    connection = None
    cursor = None
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        exclusive_type = request.args.get('exclusive_type', '')  # 独家类型：exclusive(独家), non_exclusive(非独家)
        custom_tags = request.args.get('custom_tags', '')  # 自定义标签，多个用逗号分隔
        sort_by = request.args.get('sort_by', 'sales_amount')  # 排序字段：sales_amount(销售额), sales(销量), price(价格)

        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件
        conditions = ["status = 1"]  # 只显示正常状态且未隐藏的商品
        params = []

        # 独家类型过滤
        if exclusive_type == 'exclusive':
            conditions.append("tag = '独家'")
        elif exclusive_type == 'non_exclusive':
            conditions.append("(tag != '独家' OR tag IS NULL)")

        # 自定义标签过滤
        if custom_tags:
            tag_list = [tag.strip() for tag in custom_tags.split(',') if tag.strip()]
            if tag_list:
                tag_conditions = []
                for tag in tag_list:
                    tag_conditions.append("tag LIKE %s")
                    params.append(f"%{tag}%")
                conditions.append(f"({' OR '.join(tag_conditions)})")

        # 构建WHERE子句
        where_clause = " AND ".join(conditions)

        # 排序字段验证
        valid_sort_fields = ['sales_amount', 'sales', 'price']
        if sort_by not in valid_sort_fields:
            sort_by = 'sales_amount'

        # 按商品名称合并查询
        merge_query = f"""
        SELECT
            product_name,
            MAX(product_id) as product_id,
            AVG(price) as price,
            SUM(sales) as sales,
            SUM(sales_amount) as sales_amount,
            MAX(product_image_url) as product_image_url,
            MAX(merchant_name) as merchant_name,
            MAX(category) as category,
            MAX(tag) as tag,
            AVG(store_score) as store_score,
            MAX(activity_id) as activity_id,
            MAX(merchant_commission) as merchant_commission,
            MAX(investment_commission) as investment_commission,
            MAX(create_time) as create_time,
            COUNT(*) as product_count
        FROM product
        WHERE {where_clause}
        GROUP BY product_name
        ORDER BY {sort_by} DESC, sales DESC, price DESC
        """

        cursor.execute(merge_query, params)
        all_products = cursor.fetchall()

        # 计算总数（按商品名称合并后的数量）
        total = len(all_products)

        # 分页处理
        offset = (page - 1) * page_size
        products = all_products[offset:offset + page_size]

        # 处理数据格式
        for i, product in enumerate(products):
            product['rank'] = offset + i + 1  # 添加排名
            product['sales_amount'] = round(float(product['sales_amount']) if product['sales_amount'] else 0.0, 2)
            product['price'] = round(float(product['price']) if product['price'] else 0.0, 2)
            product['store_score'] = round(float(product['store_score']) if product['store_score'] else 0.0, 2)

            # 格式化创建时间
            if product['create_time']:
                product['create_time'] = product['create_time'].strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'code': 0,
            'message': '获取爆品排行榜成功',
            'data': {
                'products': products,
                'total': total,
                'page': page,
                'page_size': page_size,
                'sort_by': sort_by
            }
        })

    except Exception as e:
        logging.error(f"获取爆品排行榜失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取爆品排行榜失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/toggle-hidden/<product_id>', methods=['PUT'])
def toggle_product_hidden(product_id):
    """切换商品隐藏状态"""
    connection = None
    cursor = None
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_role = payload.get('role', 'business')
            user_id = payload.get('user_id')

            # 检查权限：只有管理员或有隐藏商品权限的运营可以操作
            if user_role == 'admin':
                # 管理员有所有权限
                pass
            elif user_role == 'operation':
                # 检查运营用户是否有隐藏商品权限
                connection = get_connection()
                cursor = connection.cursor(dictionary=True)

                perm_query = "SELECT perm_hide_product FROM business_user WHERE id = %s"
                cursor.execute(perm_query, (user_id,))
                perm_result = cursor.fetchone()

                if not perm_result or perm_result['perm_hide_product'] != 1:
                    return jsonify({
                        'code': 403,
                        'message': '权限不足，无法操作商品隐藏状态',
                        'data': None
                    }), 403
            else:
                return jsonify({
                    'code': 403,
                    'message': '权限不足，只有管理员和运营可以操作商品隐藏状态',
                    'data': None
                }), 403

        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401

        # 获取请求数据
        data = request.json
        if not data or 'is_hidden' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供隐藏状态',
                'data': None
            }), 400

        is_hidden = data['is_hidden']

        # 如果还没有数据库连接，创建连接
        if not connection:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)

        # 检查商品是否存在
        check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
        cursor.execute(check_query, (product_id,))
        result = cursor.fetchone()
        if not result or result['count'] == 0:
            return jsonify({
                'code': 404,
                'message': f"商品不存在: {product_id}",
                'data': None
            }), 404

        # 更新隐藏状态
        update_query = "UPDATE product SET is_hidden = %s WHERE product_id = %s"
        cursor.execute(update_query, (1 if is_hidden else 0, product_id))
        connection.commit()

        action = '隐藏' if is_hidden else '显示'
        return jsonify({
            'code': 0,
            'message': f'{action}商品成功',
            'data': {
                'product_id': product_id,
                'is_hidden': is_hidden
            }
        })

    except Exception as e:
        print(f"切换商品隐藏状态失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'切换商品隐藏状态失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/update-scores/<product_id>', methods=['PUT'])
def update_product_scores(product_id):
    """更新商品评分"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空',
                'data': None
            }), 400
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查商品是否存在
        check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
        cursor.execute(check_query, [product_id])
        result = cursor.fetchone()
        if not result or result['count'] == 0:
            return jsonify({
                'code': 404,
                'message': f"商品不存在: {product_id}",
                'data': None
            }), 404
        
        # 准备更新数据
        score_fields = [
            'control_commission_score', 'cooperation_score', 'trust_score',
            'cost_performance_score', 'routine_score', 'assist_broadcast_score'
        ]
        
        # 构建SQL语句
        update_parts = []
        params = []
        
        for field in score_fields:
            if field in data and data[field] is not None:
                # 验证评分范围
                score = int(data[field])
                if score < 1 or score > 10:
                    return jsonify({
                        'code': 400,
                        'message': f'{field} 评分必须在1-10之间',
                        'data': None
                    }), 400
                update_parts.append(f"{field} = %s")
                params.append(score)
        
        if not update_parts:
            return jsonify({
                'code': 400,
                'message': '没有需要更新的评分字段',
                'data': None
            }), 400
        
        query = f"UPDATE product SET {', '.join(update_parts)}, update_time = NOW() WHERE product_id = %s"
        params.append(product_id)
        
        # 执行更新
        cursor.execute(query, params)
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '更新商品评分成功',
            'data': {
                'product_id': product_id
            }
        })
    except Exception as e:
        logging.error(f"更新商品评分失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新商品评分失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/update-remark/<product_id>', methods=['PUT'])
def update_product_remark(product_id):
    """更新商品备注"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data or 'remark' not in data:
            return jsonify({
                'code': 400,
                'message': '备注内容不能为空',
                'data': None
            }), 400
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查商品是否存在
        check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
        cursor.execute(check_query, [product_id])
        result = cursor.fetchone()
        if not result or result['count'] == 0:
            return jsonify({
                'code': 404,
                'message': f"商品不存在: {product_id}",
                'data': None
            }), 404
        
        # 执行更新
        query = "UPDATE product SET remark = %s, update_time = NOW() WHERE product_id = %s"
        cursor.execute(query, [data['remark'], product_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '更新商品备注成功',
            'data': {
                'product_id': product_id,
                'remark': data['remark']
            }
        })
    except Exception as e:
        logging.error(f"更新商品备注失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新商品备注失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/batch-update-tags', methods=['PUT'])
def batch_update_product_tags():
    """批量更新商品标签"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data or 'product_ids' not in data or 'is_exclusive' not in data:
            return jsonify({
                'code': 400,
                'message': '商品ID列表和独家标志不能为空',
                'data': None
            }), 400

        product_ids = data['product_ids']
        is_exclusive = data['is_exclusive']
        sync_by_name = data.get('sync_by_name', False)
        
        if not product_ids or len(product_ids) == 0:
            return jsonify({
                'code': 400,
                'message': '商品ID列表不能为空',
                'data': None
            }), 400
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 设置标签值
        tag_value = '独家' if is_exclusive else None

        if sync_by_name:
            # 按商品名称同步：先获取选中商品的名称
            placeholders = ', '.join(['%s'] * len(product_ids))
            name_query = f"SELECT DISTINCT product_name FROM product WHERE product_id IN ({placeholders})"
            cursor.execute(name_query, product_ids)
            product_names = [row['product_name'] for row in cursor.fetchall()]

            if product_names:
                # 更新所有同名商品
                name_placeholders = ', '.join(['%s'] * len(product_names))
                query = f"UPDATE product SET tag = %s, update_time = NOW() WHERE product_name IN ({name_placeholders})"
                cursor.execute(query, [tag_value] + product_names)
        else:
            # 只更新选中的商品
            placeholders = ', '.join(['%s'] * len(product_ids))
            query = f"UPDATE product SET tag = %s, update_time = NOW() WHERE product_id IN ({placeholders})"
            cursor.execute(query, [tag_value] + product_ids)

        affected_rows = cursor.rowcount
        connection.commit()

        return jsonify({
            'code': 0,
            'message': f'批量更新商品标签成功，共更新{affected_rows}条记录',
            'data': {
                'affected_count': affected_rows,
                'is_exclusive': is_exclusive,
                'sync_by_name': sync_by_name
            }
        })
    except Exception as e:
        logging.error(f"批量更新商品标签失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'批量更新商品标签失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@product_bp.route('/add-promotion', methods=['POST'])
def add_promotion():
    """添加商品推广"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            business_name = payload['name']  # 从token中获取商务名称
            role = payload.get('role', 'business')
            
            # 只有商务角色可以添加推广
            if role != 'business' and role != 'admin':
                return jsonify({
                    'code': 403,
                    'message': '权限不足，只有商务角色可以添加推广',
                    'data': None
                }), 403
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        data = request.json
        if not data or 'product_id' not in data or 'talent_id' not in data or 'talent_name' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供商品ID、达人ID和达人名称',
                'data': None
            }), 400
        
        product_id = data['product_id']
        talent_id = data['talent_id']
        talent_name = data['talent_name']
        
        # 从数据库获取cookie配置
        cookie_string = get_kuaishou_cookie()
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查是否已存在相同的推广记录
        check_query = """
        SELECT COUNT(*) as count FROM product_promotion 
        WHERE product_id = %s AND talent_id = %s AND talent_name = %s AND business_name = %s
        """
        cursor.execute(check_query, [product_id, talent_id, talent_name, business_name])
        result = cursor.fetchone()
        
        if result and result['count'] > 0:
            return jsonify({
                'code': 400,
                'message': '该达人已为此商品添加推广',
                'data': None
            })
        
        # 获取商品的活动ID
        product_query = "SELECT activity_id FROM product WHERE product_id = %s"
        cursor.execute(product_query, [product_id])
        product_result = cursor.fetchone()
        
        if not product_result or not product_result['activity_id']:
            return jsonify({
                'code': 400,
                'message': '商品未关联活动，无法添加推广',
                'data': None
            })
        
        activity_id = product_result['activity_id']
        
        # 调用推广管理脚本 - 保留原有逻辑
        promotion_result = None
        if PromotionManager:
            try:
                # 创建推广管理器
                manager = PromotionManager(cookie_string)
                
                # 调用快手API添加推广
                promotion_result = manager.add_promotion(
                    promoter_ids=int(talent_id),
                    activity_id=int(activity_id)
                )
                
                print(f"快手API推广结果: {promotion_result}")
                
            except Exception as e:
                logging.error(f"调用推广管理脚本失败: {str(e)}")
                promotion_result = {
                    "success": False,
                    "message": f"调用推广管理脚本失败: {str(e)}"
                }
        else:
            logging.warning("推广管理模块未导入，跳过快手API调用")
        
        
        # 事务开始 - 同时更新两个表
        try:
            # 1. 保存到原有的product_promotion表
            insert_query = """
            INSERT INTO product_promotion (product_id, talent_id, talent_name, business_name)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(insert_query, [product_id, talent_id, talent_name, business_name])
            
            # 2. 同时更新商品表中的对接商务字段
            update_query = """
            UPDATE product 
            SET business_contact = %s
            WHERE product_id = %s AND (business_contact IS NULL OR business_contact = '')
            """
            cursor.execute(update_query, [business_name, product_id])
            
            # 提交事务
            connection.commit()
            
            # 返回成功结果
            return jsonify({
                'code': 0,
                'message': '添加推广成功',
                'data': {
                    'local_success': True,
                    'api_success': True,
                    'api_message': promotion_result.get('message', '') if promotion_result else '推广管理模块未导入',
                    'business_contact': business_name
                }
            })
        except Exception as e:
            # 回滚事务
            connection.rollback()
            raise e
            
    except Exception as e:
        print(f"添加推广失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'添加推广失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/my-promotions', methods=['GET'])
def get_my_promotions():
    """获取我的推广商品列表"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            business_name = payload['name']  # 从token中获取商务名称
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        product_name = request.args.get('product_name', '')
        product_id = request.args.get('product_id', '')
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 构建查询条件
        params = [business_name]
        where_clause = "pp.business_name = %s"
        
        if product_name:
            where_clause += " AND p.product_name LIKE %s"
            params.append(f"%{product_name}%")
        
        if product_id:
            where_clause += " AND p.product_id = %s"
            params.append(product_id)
            
        # 添加更多搜索条件
        talent_id = request.args.get('talent_id', '')
        talent_name = request.args.get('talent_name', '')
        min_price = request.args.get('min_price', '')
        max_price = request.args.get('max_price', '')
        category = request.args.get('category', '')
        tag = request.args.get('tag', '')
        sort_by = request.args.get('sort_by', 'promotion_time')
        sort_order = request.args.get('sort_order', 'desc')
        
        if talent_id:
            where_clause += " AND pp.talent_id = %s"
            params.append(talent_id)
            
        if talent_name:
            where_clause += " AND pp.talent_name LIKE %s"
            params.append(f"%{talent_name}%")
            
        if min_price:
            where_clause += " AND p.price >= %s"
            params.append(float(min_price))
            
        if max_price:
            where_clause += " AND p.price <= %s"
            params.append(float(max_price))
            
        if category:
            where_clause += " AND p.category LIKE %s"
            params.append(f"%{category}%")
            
        if tag:
            where_clause += " AND p.tag = %s"
            params.append(tag)
        
        # 获取总数
        count_query = f"""
        SELECT COUNT(*) as total 
        FROM product_promotion pp
        JOIN product p ON pp.product_id = p.product_id
        WHERE {where_clause}
        """
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 构建排序语句
        valid_sort_fields = ['promotion_time', 'p.product_id', 'p.product_name', 'p.price', 'p.create_time', 'p.stock', 'p.sales', 'pp.talent_id', 'pp.talent_name']
        if sort_by not in valid_sort_fields:
            sort_by = 'promotion_time'
        
        if sort_order.upper() not in ['ASC', 'DESC']:
            sort_order = 'DESC'
        
        # 查询数据
        query = f"""
        SELECT p.*, pp.talent_id, pp.talent_name, pp.create_time as promotion_time
        FROM product_promotion pp
        JOIN product p ON pp.product_id = p.product_id
        WHERE {where_clause}
        ORDER BY {sort_by} {sort_order}
        LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        cursor.execute(query, params)
        promotions = cursor.fetchall()
        
        # 格式化数据，与商品管理模块保持一致
        formatted_promotions = []
        for p in promotions:
            formatted_promotions.append({
                'id': p['product_id'],
                'name': p['product_name'],
                'price': float(p['price']),
                'merchant_id': p.get('merchant_id', ''),
                'merchant_name': p.get('merchant_name', ''),
                'merchant_commission': p.get('merchant_commission', '0%'),
                'talent_commission': p.get('investment_commission', '0%'),
                'investment_commission': p.get('investment_commission', '0%'),
                'create_time': p['create_time'].strftime('%Y-%m-%d %H:%M:%S') if p.get('create_time') else '',
                'image_url': p.get('product_image_url', ''),
                'category': p.get('category', ''),
                'stock': p.get('stock', 0),
                'sales': p.get('sales', 0),
                'status': p.get('status', 1),
                'store_score': p.get('store_score', ''),
                'activity_id': p.get('activity_id', ''),
                'control_commission_score': p.get('control_commission_score'),
                'cooperation_score': p.get('cooperation_score'),
                'trust_score': p.get('trust_score'),
                'cost_performance_score': p.get('cost_performance_score'),
                'routine_score': p.get('routine_score'),
                'assist_broadcast_score': p.get('assist_broadcast_score'),
                'remark': p.get('remark'),
                'tag': p.get('tag'),
                'talent_id': p['talent_id'],
                'talent_name': p['talent_name'],
                'promotion_time': p['promotion_time'].strftime('%Y-%m-%d %H:%M:%S') if p.get('promotion_time') else '',
                'business_contact': p.get('business_contact', ''),
                'operation_contact': p.get('operation_contact', ''),
                'merchant_commission_rate': p.get('merchant_commission_rate', 0),
                'team_leader_commission_rate': p.get('team_leader_commission_rate', 0),
                'service_fee_rate': p.get('service_fee_rate', 0),
                'reserved_service_fee_rate': p.get('reserved_service_fee_rate', 0)
            })
        
        return jsonify({
            'code': 0,
            'message': '获取推广商品列表成功',
            'data': {
                'list': formatted_promotions,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        })
    except Exception as e:
        print(f"获取推广商品列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取推广商品列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/remove-promotion', methods=['POST'])
def remove_promotion():
    """移除商品推广"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            business_name = payload['name']  # 从token中获取商务名称
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        data = request.json
        if not data or 'product_id' not in data or 'talent_id' not in data or 'talent_name' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供商品ID、达人ID和达人名称',
                'data': None
            }), 400
        
        product_id = data['product_id']
        talent_id = data['talent_id']
        talent_name = data['talent_name']
        
        # 从数据库获取cookie配置
        cookie_string = get_kuaishou_cookie()
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 获取商品的活动ID
        product_query = "SELECT activity_id FROM product WHERE product_id = %s"
        cursor.execute(product_query, [product_id])
        product_result = cursor.fetchone()
        
        activity_id = None
        if product_result and product_result['activity_id']:
            activity_id = product_result['activity_id']
        
        # 调用推广管理脚本
        promotion_result = None
        if PromotionManager and activity_id:
            try:
                # 创建推广管理器
                manager = PromotionManager(cookie_string)
                
                # 调用快手API移除推广
                promotion_result = manager.remove_promotion(
                    promoter_ids=int(talent_id),
                    activity_id=int(activity_id)
                )
                
                logging.info(f"快手API移除推广结果: {promotion_result}")
                
            except Exception as e:
                logging.error(f"调用推广管理脚本失败: {str(e)}")
                promotion_result = {
                    "success": False,
                    "message": f"调用推广管理脚本失败: {str(e)}"
                }
        else:
            logging.warning("推广管理模块未导入或商品未关联活动，跳过快手API调用")
        
        
        # 删除本地数据库中的推广记录（只有在不是活动状态错误时才删除）
        delete_query = """
        DELETE FROM product_promotion 
        WHERE product_id = %s AND talent_id = %s AND talent_name = %s AND business_name = %s
        """
        cursor.execute(delete_query, [product_id, talent_id, talent_name, business_name])
        connection.commit()
        
        # 返回结果
        if promotion_result and promotion_result.get('success'):
            return jsonify({
                'code': 0,
                'message': '移除推广成功（本地数据库和快手API）',
                'data': {
                    'local_success': True,
                    'api_success': True,
                    'api_message': promotion_result.get('message', '')
                }
            })
        else:
            return jsonify({
                'code': 0,
                'message': '移除推广成功（仅本地数据库）',
                'data': {
                    'local_success': True,
                    'api_success': False,
                    'api_message': promotion_result.get('message', '') if promotion_result else '推广管理模块未导入或商品未关联活动'
                }
            })
            
    except Exception as e:
        print(f"移除推广失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'移除推广失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@product_bp.route('/update-rates/<product_id>', methods=['PUT'])
def update_product_rates(product_id):
    """更新商品费率"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空',
                'data': None
            }), 400
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查商品是否存在
        check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
        cursor.execute(check_query, [product_id])
        result = cursor.fetchone()
        if not result or result['count'] == 0:
            return jsonify({
                'code': 404,
                'message': f"商品不存在: {product_id}",
                'data': None
            }), 404
        
        # 准备更新数据
        update_parts = []
        params = []
        
        # 处理服务费率 (investment_commission)
        if 'serviceFeeRate' in data and data['serviceFeeRate'] is not None:
            rate = int(data['serviceFeeRate'])
            if rate < 0 or rate > 100:
                return jsonify({
                    'code': 400,
                    'message': '服务费率必须在0-100之间',
                    'data': None
                }), 400
            update_parts.append("investment_commission = %s")
            params.append(f"{rate}%")
            # 同时更新数值字段
            update_parts.append("service_fee_rate = %s")
            params.append(rate)
        
        # 处理预留服务费率
        if 'reservedServiceFeeRate' in data and data['reservedServiceFeeRate'] is not None:
            rate = int(data['reservedServiceFeeRate'])
            if rate < 0 or rate > 100:
                return jsonify({
                    'code': 400,
                    'message': '预留服务费率必须在0-100之间',
                    'data': None
                }), 400
            update_parts.append("service_fee_rate = %s")
            params.append(rate)
        
        # 处理商家返佣率
        if 'merchantCommissionRate' in data and data['merchantCommissionRate'] is not None:
            rate = int(data['merchantCommissionRate'])
            if rate < 0 or rate > 100:
                return jsonify({
                    'code': 400,
                    'message': '商家返佣率必须在0-100之间',
                    'data': None
                }), 400
            # 只更新返佣率字段，不影响佣金率字段
            update_parts.append("merchant_commission_rate = %s")
            params.append(rate)
            # 不要更新 merchant_commission 字段
            # update_parts.append("merchant_commission = %s")
            # params.append(f"{rate}%")
        
        # 处理团长返佣率
        if 'teamLeaderCommissionRate' in data and data['teamLeaderCommissionRate'] is not None:
            rate = int(data['teamLeaderCommissionRate'])
            if rate < 0 or rate > 100:
                return jsonify({
                    'code': 400,
                    'message': '团长返佣率必须在0-100之间',
                    'data': None
                }), 400
            update_parts.append("team_leader_commission_rate = %s")
            params.append(rate)
        
        if not update_parts:
            return jsonify({
                'code': 400,
                'message': '没有需要更新的费率字段',
                'data': None
            }), 400
        
        query = f"UPDATE product SET {', '.join(update_parts)}, update_time = NOW() WHERE product_id = %s"
        params.append(product_id)
        
        # 执行更新
        cursor.execute(query, params)
        connection.commit()
        
        # 获取更新后的商品信息
        select_query = """
        SELECT product_id, investment_commission, merchant_commission,
               service_fee_rate, service_fee_rate as reserved_service_fee_rate,
               merchant_commission_rate, team_leader_commission_rate
        FROM product WHERE product_id = %s
        """
        cursor.execute(select_query, [product_id])
        updated_product = cursor.fetchone()
        
        return jsonify({
            'code': 0,
            'message': '更新商品费率成功',
            'data': updated_product
        })
    except Exception as e:
        logging.error(f"更新商品费率失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新商品费率失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@product_bp.route('/apply-sample', methods=['POST'])
def apply_sample():
    """申请商品样品"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            business_user_id = payload['user_id']  # 修复：使用正确的键名
            business_name = payload['name']
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空',
                'data': None
            }), 400
        
        # 验证必填字段
        required_fields = ['product_id', 'receiver_name', 'receiver_phone', 'receiver_address', 'sample_count']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'code': 400,
                    'message': f'缺少必填字段: {field}',
                    'data': None
                }), 400
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查商品是否存在，并获取商品信息（包括对接商务和对接运营）
        check_query = """
        SELECT product_id, product_name, product_image_url, 
               business_contact, operation_contact
        FROM product 
        WHERE product_id = %s
        """
        cursor.execute(check_query, [data['product_id']])
        product = cursor.fetchone()
        
        if not product:
            return jsonify({
                'code': 404,
                'message': f"商品不存在: {data['product_id']}",
                'data': None
            }), 404
        
        # 从商品表获取对接商务和对接运营信息
        business_contact = product.get('business_contact', '')
        operation_contact = product.get('operation_contact', '')
        
        # 插入样品申请记录
        insert_query = """
        INSERT INTO product_sample (
            product_id, business_user_id, business_name, receiver_name, 
            receiver_phone, receiver_wechat, receiver_address, sample_count, 
            remark, status
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(insert_query, [
            data['product_id'],
            business_user_id,
            business_name,
            data['receiver_name'],
            data['receiver_phone'],
            data.get('receiver_wechat', ''),
            data['receiver_address'],
            data['sample_count'],
            data.get('remark', ''),
            0,  # 初始状态：申请中
        ])
        
        sample_id = cursor.lastrowid
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '样品申请提交成功',
            'data': {
                'id': sample_id,
                'business_contact': business_contact,
                'operation_contact': operation_contact
            }
        })
    except Exception as e:
        logging.error(f"申请样品失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'申请样品失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/samples', methods=['GET'])
def get_samples():
    """获取样品申请列表"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            # 检查键名是否存在
            user_id = payload.get('id') or payload.get('user_id')
            user_role = payload.get('role')
            user_name = payload.get('name') or payload.get('username')
            
            # 如果找不到必要的信息，返回错误
            if not user_id or not user_role:
                return jsonify({
                    'code': 401,
                    'message': '令牌中缺少必要的用户信息',
                    'data': None
                }), 401
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 10, type=int)
        product_name = request.args.get('product_name', '')
        product_id = request.args.get('product_id', '')
        tracking_number = request.args.get('tracking_number', '')
        business_name = request.args.get('business_name', '')
        operation_name = request.args.get('operation_name', '')
        status = request.args.get('status', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 构建查询条件
        conditions = []
        params = []
        
        # 根据角色过滤数据
        if user_role == 'business':
            conditions.append("ps.business_user_id = %s")
            params.append(user_id)
        elif user_role == 'operation':
            # 运营角色可以看到所有记录或者自己处理的记录
            pass
        elif user_role != 'admin':
            # 非管理员、商务、运营角色无权访问
            return jsonify({
                'code': 403,
                'message': '无权访问',
                'data': None
            }), 403
        
        # 添加其他过滤条件
        if product_name:
            conditions.append("p.product_name LIKE %s")
            params.append(f"%{product_name}%")
        
        if product_id:
            conditions.append("ps.product_id = %s")
            params.append(product_id)
        
        if tracking_number:
            conditions.append("ps.tracking_number LIKE %s")
            params.append(f"%{tracking_number}%")
        
        if business_name:
            conditions.append("ps.business_name = %s")
            params.append(business_name)
        
        if operation_name:
            conditions.append("ps.operation_name = %s")
            params.append(operation_name)
        
        if status != '':
            conditions.append("ps.status = %s")
            params.append(int(status))
        
        if start_date and end_date:
            conditions.append("DATE(ps.create_time) BETWEEN %s AND %s")
            params.extend([start_date, end_date])
        
        # 构建WHERE子句
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        # 获取总记录数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM product_sample ps
        LEFT JOIN product p ON ps.product_id = p.product_id
        WHERE {where_clause}
        """
        
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 查询样品列表
        query = f"""
        SELECT ps.*, p.product_name, p.product_image_url
        FROM product_sample ps
        LEFT JOIN product p ON ps.product_id = p.product_id
        WHERE {where_clause}
        ORDER BY ps.create_time DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([page_size, (page - 1) * page_size])
        cursor.execute(query, params)
        samples = cursor.fetchall()
        
        # 格式化日期时间
        for sample in samples:
            if 'create_time' in sample and sample['create_time']:
                sample['create_time'] = sample['create_time'].strftime('%Y-%m-%d %H:%M:%S')
            if 'update_time' in sample and sample['update_time']:
                sample['update_time'] = sample['update_time'].strftime('%Y-%m-%d %H:%M:%S')
        
        return jsonify({
            'code': 0,
            'message': '获取样品列表成功',
            'data': {
                'list': samples,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        })
    except Exception as e:
        logging.error(f"获取样品列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取样品列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/ship-sample/<int:sample_id>', methods=['POST'])
def ship_sample(sample_id):
    """发货样品"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_id = payload['user_id']  # 修复：使用正确的键名
            user_role = payload['role']
            user_name = payload['name']
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 只有运营和管理员可以发货
        if user_role not in ['operation', 'admin']:
            return jsonify({
                'code': 403,
                'message': '无权操作',
                'data': None
            }), 403
        
        # 获取请求数据
        data = request.json
        if not data or 'tracking_number' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供物流单号',
                'data': None
            }), 400
        
        tracking_number = data['tracking_number']
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查样品申请是否存在
        check_query = "SELECT id, status FROM product_sample WHERE id = %s"
        cursor.execute(check_query, [sample_id])
        sample = cursor.fetchone()
        
        if not sample:
            return jsonify({
                'code': 404,
                'message': f"样品申请不存在: {sample_id}",
                'data': None
            }), 404
        
        # 检查状态是否为申请中
        if sample['status'] != 0:
            return jsonify({
                'code': 400,
                'message': '只能发货申请中的样品',
                'data': None
            }), 400
        
        # 更新样品状态为已发货
        update_query = """
        UPDATE product_sample
        SET status = 1, tracking_number = %s, operation_user_id = %s, operation_name = %s
        WHERE id = %s
        """
        
        cursor.execute(update_query, [tracking_number, user_id, user_name, sample_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '样品发货成功',
            'data': {
                'id': sample_id
            }
        })
    except Exception as e:
        logging.error(f"样品发货失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'样品发货失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/reject-sample/<int:sample_id>', methods=['POST'])
def reject_sample(sample_id):
    """拒绝样品申请"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_id = payload['user_id']  # 修复：使用正确的键名
            user_role = payload['role']
            user_name = payload['name']
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 只有运营和管理员可以拒绝申请
        if user_role not in ['operation', 'admin']:
            return jsonify({
                'code': 403,
                'message': '无权操作',
                'data': None
            }), 403
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查样品申请是否存在
        check_query = "SELECT id, status FROM product_sample WHERE id = %s"
        cursor.execute(check_query, [sample_id])
        sample = cursor.fetchone()
        
        if not sample:
            return jsonify({
                'code': 404,
                'message': f"样品申请不存在: {sample_id}",
                'data': None
            }), 404
        
        # 检查状态是否为申请中
        if sample['status'] != 0:
            return jsonify({
                'code': 400,
                'message': '只能拒绝申请中的样品',
                'data': None
            }), 400
        
        # 更新样品状态为已拒绝
        update_query = """
        UPDATE product_sample
        SET status = 3, operation_user_id = %s, operation_name = %s
        WHERE id = %s
        """
        
        cursor.execute(update_query, [user_id, user_name, sample_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '已拒绝样品申请',
            'data': {
                'id': sample_id
            }
        })
    except Exception as e:
        logging.error(f"拒绝样品申请失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'拒绝样品申请失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/confirm-sample/<int:sample_id>', methods=['POST'])
def confirm_sample(sample_id):
    """确认收到样品"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_id = payload['user_id']  # 修复：使用正确的键名
            user_role = payload['role']
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查样品申请是否存在
        check_query = "SELECT id, status, business_user_id FROM product_sample WHERE id = %s"
        cursor.execute(check_query, [sample_id])
        sample = cursor.fetchone()
        
        if not sample:
            return jsonify({
                'code': 404,
                'message': f"样品申请不存在: {sample_id}",
                'data': None
            }), 404
        
        # 检查状态是否为已发货
        if sample['status'] != 1:
            return jsonify({
                'code': 400,
                'message': '只能确认已发货的样品',
                'data': None
            }), 400
        
        # 检查是否是申请人本人或管理员
        if user_role != 'admin' and int(sample['business_user_id']) != int(user_id):
            return jsonify({
                'code': 403,
                'message': '无权操作',
                'data': None
            }), 403
        
        # 更新样品状态为已收货
        update_query = """
        UPDATE product_sample
        SET status = 2
        WHERE id = %s
        """
        
        cursor.execute(update_query, [sample_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '已确认收货',
            'data': {
                'id': sample_id
            }
        })
    except Exception as e:
        logging.error(f"确认收货失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'确认收货失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 
@product_bp.route('/assign-operation/<product_id>', methods=['PUT'])
def assign_operation(product_id):
    """分配运营人员"""
    connection = None
    cursor = None
    try:
        data = request.json
        if not data or 'operation_contact' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供运营人员姓名',
                'data': None
            }), 400
        
        operation_contact = data['operation_contact']
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 更新商品表中的对接运营字段
        update_query = """
        UPDATE product 
        SET operation_contact = %s
        WHERE product_id = %s
        """
        cursor.execute(update_query, [operation_contact, product_id])
        connection.commit()
        
        # 返回成功结果
        return jsonify({
            'code': 0,
            'message': '分配运营成功',
            'data': {
                'product_id': product_id,
                'operation_contact': operation_contact
            }
        })
            
    except Exception as e:
        print(f"分配运营失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'分配运营失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/update-sample/<int:sample_id>', methods=['PUT'])
def update_sample(sample_id):
    """更新样品申请"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_id = payload['user_id']
            user_role = payload['role']
            user_name = payload['name']
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401

        # 只有运营和管理员可以编辑样品申请
        if user_role not in ['operation', 'admin']:
            return jsonify({
                'code': 403,
                'message': '无权操作',
                'data': None
            }), 403

        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请提供更新数据',
                'data': None
            }), 400

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 检查样品申请是否存在
        check_query = f"SELECT id FROM product_sample WHERE id = {sample_id}"
        cursor.execute(check_query)
        sample = cursor.fetchone()

        if not sample:
            return jsonify({
                'code': 404,
                'message': f"样品申请不存在: {sample_id}",
                'data': None
            }), 404

        # 构建更新查询
        update_fields = []

        if 'receiver_name' in data:
            safe_name = data['receiver_name'].replace("'", "''")
            update_fields.append(f"receiver_name = '{safe_name}'")

        if 'receiver_phone' in data:
            safe_phone = data['receiver_phone'].replace("'", "''")
            update_fields.append(f"receiver_phone = '{safe_phone}'")

        if 'receiver_wechat' in data:
            safe_wechat = data['receiver_wechat'].replace("'", "''")
            update_fields.append(f"receiver_wechat = '{safe_wechat}'")

        if 'receiver_address' in data:
            safe_address = data['receiver_address'].replace("'", "''")
            update_fields.append(f"receiver_address = '{safe_address}'")

        if 'sample_count' in data:
            update_fields.append(f"sample_count = {data['sample_count']}")

        if 'tracking_number' in data:
            safe_tracking = data['tracking_number'].replace("'", "''")
            update_fields.append(f"tracking_number = '{safe_tracking}'")

        if 'status' in data:
            update_fields.append(f"status = {data['status']}")

        if 'remark' in data:
            safe_remark = data['remark'].replace("'", "''")
            update_fields.append(f"remark = '{safe_remark}'")

        if not update_fields:
            return jsonify({
                'code': 400,
                'message': '没有提供需要更新的字段',
                'data': None
            }), 400

        # 添加更新时间和操作人
        update_fields.append("update_time = NOW()")
        update_fields.append(f"operation_user_id = {user_id}")
        update_fields.append("operation_name = '%s'" % user_name.replace("'", "''"))

        # 执行更新
        update_query = f"UPDATE product_sample SET {', '.join(update_fields)} WHERE id = {sample_id}"
        cursor.execute(update_query)
        connection.commit()

        return jsonify({
            'code': 0,
            'message': '更新样品申请成功',
            'data': {
                'sample_id': sample_id
            }
        })

    except Exception as e:
        print(f"更新样品申请失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新样品申请失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@product_bp.route('/talent-bindings/<product_id>', methods=['GET'])
def get_product_talent_bindings(product_id):
    """获取商品绑定的达人信息"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 查询绑定的达人信息
        query = f"""
        SELECT pp.talent_id, pp.talent_name, pp.business_name,
               DATE_FORMAT(pp.create_time, '%Y-%m-%d %H:%i:%s') as binding_time
        FROM product_promotion pp
        WHERE pp.product_id = '{product_id}'
        ORDER BY pp.create_time DESC
        """
        cursor.execute(query)
        bindings = cursor.fetchall()

        return jsonify({
            'code': 0,
            'message': '获取商品达人绑定信息成功',
            'data': {
                'product_id': product_id,
                'bindings': bindings,
                'total_count': len(bindings)
            }
        })

    except Exception as e:
        print(f"获取商品达人绑定信息失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取商品达人绑定信息失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()