<template>
  <div class="business-performance-ranking">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2 class="title">
            <el-icon class="title-icon"><TrendCharts /></el-icon>
            商务业绩排行榜
          </h2>
          <div class="header-actions">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
              size="small"
            />
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-area">
        <el-form :inline="true" :model="searchForm" class="filter-form">
          <el-form-item label="商务名称">
            <el-input v-model="searchForm.businessName" placeholder="请输入商务名称" />
          </el-form-item>
          <el-form-item label="排序方式">
            <el-select
              v-model="searchForm.sortBy"
              placeholder="请选择排序方式"
              @change="handleSearch"
            >
              <el-option label="净预估服务费降序" value="net_estimated_desc" />
              <el-option label="净预估服务费升序" value="net_estimated_asc" />
              <el-option label="净结算服务费降序" value="net_settled_desc" />
              <el-option label="净结算服务费升序" value="net_settled_asc" />
              <el-option label="总GMV降序" value="total_amount_desc" />
              <el-option label="总GMV升序" value="total_amount_asc" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 排行榜列表 -->
      <div class="ranking-list" v-loading="loading">
        <div
          v-for="(item, index) in tableData"
          :key="item.business_name"
          class="ranking-item"
          :class="getRankingClass((currentPage - 1) * pageSize + index + 1)"
        >
          <!-- 排名徽章 -->
          <div
            class="rank-badge"
            :class="getRankBadgeClass((currentPage - 1) * pageSize + index + 1)"
          >
            <span v-if="(currentPage - 1) * pageSize + index + 1 <= 3" class="rank-icon">
              <el-icon v-if="(currentPage - 1) * pageSize + index + 1 === 1"><Trophy /></el-icon>
              <el-icon v-else-if="(currentPage - 1) * pageSize + index + 1 === 2"
                ><Medal
              /></el-icon>
              <el-icon v-else><Star /></el-icon>
            </span>
            <span v-else class="rank-number">{{ (currentPage - 1) * pageSize + index + 1 }}</span>
          </div>

          <!-- 商务信息 -->
          <div class="business-info">
            <div class="business-avatar">
              <el-avatar :size="40" :src="item.avatar" />
            </div>
            <div class="business-details">
              <div class="business-name">{{ item.business_name }}</div>
              <div class="business-meta">
                <span class="order-count">总订单: {{ item.total_orders }}</span>
                <span class="avg-order">平均订单: ¥{{ item.avgOrderAmountStr }}</span>
              </div>
            </div>
          </div>

          <!-- 核心指标 -->
          <div class="metrics">
            <div class="metric-item primary">
              <div class="metric-label">净预估服务费</div>
              <div
                class="metric-value"
                :class="{
                  positive: item.net_service_fee > 0,
                  negative: item.net_service_fee < 0,
                }"
              >
                ¥{{ item.netServiceFeeStr }}
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">总GMV</div>
              <div class="metric-value">¥{{ item.totalAmountStr }}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">净结算服务费</div>
              <div
                class="metric-value"
                :class="{
                  positive: item.net_settled_fee > 0,
                  negative: item.net_settled_fee < 0,
                }"
              >
                ¥{{ item.netSettledFeeStr }}
              </div>
            </div>
          </div>

          <!-- 详细数据 -->
          <div class="detail-info">
            <div class="detail-section">
              <div class="section-title">收入数据</div>
              <div class="detail-row">
                <span class="detail-label">订单数:</span>
                <span class="detail-value income">{{ item.income_orders }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">GMV:</span>
                <span class="detail-value income">¥{{ item.incomeAmountStr }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">预估服务费:</span>
                <span class="detail-value income">¥{{ item.incomeServiceFeeStr }}</span>
              </div>
            </div>
            <div class="detail-section">
              <div class="section-title">支出数据</div>
              <div class="detail-row">
                <span class="detail-label">订单数:</span>
                <span class="detail-value expense">{{ item.expense_orders }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">GMV:</span>
                <span class="detail-value expense">¥{{ item.expenseAmountStr }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">预估服务费:</span>
                <span class="detail-value expense">¥{{ item.expenseServiceFeeStr }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="!loading && tableData.length === 0" description="暂无数据" />
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { TrendCharts, Search, RefreshLeft, Trophy, Medal, Star } from '@element-plus/icons-vue'

// 定义数据接口
interface BusinessData {
  business_name: string
  avatar: string
  total_orders: number
  income_orders: number
  expense_orders: number
  total_amount: number
  income_amount: number
  expense_amount: number
  income_service_fee: number
  expense_service_fee: number
  net_service_fee: number
  income_settled_fee: number
  expense_settled_fee: number
  net_settled_fee: number
  avg_order_amount: number
  // 新增字段
  income_estimated_service_fee: number
  income_settled_service_fee: number
  expense_estimated_service_fee: number
  expense_settled_service_fee: number
  totalAmountStr: string
  incomeAmountStr: string
  expenseAmountStr: string
  incomeServiceFeeStr: string
  expenseServiceFeeStr: string
  netServiceFeeStr: string
  incomeSettledFeeStr: string
  expenseSettledFeeStr: string
  netSettledFeeStr: string
  avgOrderAmountStr: string
}

// 日期范围
const dateRange = ref<[string, string]>([
  new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  new Date().toISOString().split('T')[0],
])

// 搜索表单
const searchForm = reactive({
  businessName: '',
  sortBy: 'net_estimated_desc', // 默认按净预估服务费降序
})

// 表格数据
const tableData = ref<BusinessData[]>([])
const loading = ref(false)

// 当前用户信息
const currentUser = ref<any>(null)

// 用户权限
const userPermissions = ref<any>({
  performance: {
    data: [],
    operations: [],
    modules: [],
  },
})

// 权限检查方法
const hasDataPermission = (permission: string) => {
  return userPermissions.value.performance.data.includes(permission)
}

const hasModulePermission = (permission: string) => {
  return userPermissions.value.performance.modules.includes(permission)
}

// 便捷权限检查
const canViewOwnDataOnly = computed(() => hasDataPermission('own_only'))
const canViewAllData = computed(() => hasDataPermission('all_data'))
const canAccessPerformanceStatistics = computed(() => hasModulePermission('performance_statistics'))

// 分页
const currentPage = ref(1)
const pageSize = ref(9) // 卡片显示，每页9个（3x3网格）
const total = ref(0)

// 日期变化处理
const handleDateChange = () => {
  currentPage.value = 1
  fetchData()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.businessName = ''
  searchForm.sortBy = 'net_estimated_desc'
  handleSearch()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  fetchData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchData()
}

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)

      // 解析权限信息
      if (currentUser.value.permissions) {
        try {
          const permissions =
            typeof currentUser.value.permissions === 'string'
              ? JSON.parse(currentUser.value.permissions)
              : currentUser.value.permissions
          userPermissions.value = permissions
        } catch (permError) {
          console.error('解析权限信息失败:', permError)
          // 使用默认权限
          userPermissions.value = {
            performance: { data: [], operations: [], modules: [] },
          }
        }
      }

      console.log('当前登录用户:', currentUser.value)
      console.log('业绩模块权限:', userPermissions.value.performance)
    } else {
      console.error('未找到登录用户信息')
      ElMessage.warning('未找到登录用户信息，请重新登录')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请重新登录')
  }
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      pageSize: pageSize.value,
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    if (searchForm.businessName) {
      params.businessName = searchForm.businessName
    }

    if (searchForm.sortBy) {
      params.sortBy = searchForm.sortBy
    }

    const token = localStorage.getItem('token')
    const response = await axios.get('/api/performance/statistics', {
      params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (response.data.code === 200) {
      const data = response.data.data
      tableData.value = data.list || []
      total.value = data.total || 0
    } else {
      ElMessage.error(response.data.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取商务业绩统计数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取排名样式类
const getRankingClass = (rank: number) => {
  if (rank <= 3) return 'top-three'
  if (rank <= 10) return 'top-ten'
  return ''
}

// 获取排名徽章样式类
const getRankBadgeClass = (rank: number) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  return 'normal'
}

// 生命周期
onMounted(() => {
  getCurrentUser()
  fetchData()
})
</script>

<style scoped>
.business-performance-ranking {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-icon {
  margin-right: 8px;
  font-size: 28px;
  color: #667eea;
}

.filter-area {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.filter-form {
  margin: 0;
}

.filter-form :deep(.el-form-item__label) {
  color: white;
  font-weight: 500;
}

.ranking-list {
  min-height: 400px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 16px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ranking-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ranking-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

.ranking-item:hover::before {
  opacity: 1;
}

.ranking-item.top-three {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border: 2px solid #fdcb6e;
}

.ranking-item.top-ten {
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8c0 100%);
  border: 2px solid #00b894;
}

/* 排名徽章样式 */
.rank-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 18px;
  font-weight: bold;
  margin-right: 20px;
  flex-shrink: 0;
}

.rank-badge.gold {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b4513;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
}

.rank-badge.silver {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #555;
  box-shadow: 0 4px 20px rgba(192, 192, 192, 0.4);
}

.rank-badge.bronze {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: white;
  box-shadow: 0 4px 20px rgba(205, 127, 50, 0.4);
}

.rank-badge.normal {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  box-shadow: 0 4px 20px rgba(116, 185, 255, 0.3);
}

.rank-icon {
  font-size: 24px;
}

.rank-number {
  font-size: 20px;
}

/* 商务信息样式 */
.business-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 20px;
}

.business-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.business-details {
  flex: 1;
}

.business-name {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  line-height: 1.4;
}

.business-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.order-count {
  font-size: 12px;
  color: #667eea;
  background: #e6f3ff;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.avg-order {
  font-size: 12px;
  color: #4a5568;
  font-weight: 500;
}

/* 核心指标样式 */
.metrics {
  display: flex;
  gap: 24px;
  margin-right: 20px;
}

.metric-item {
  text-align: center;
  min-width: 100px;
}

.metric-item.primary .metric-value {
  font-size: 18px;
  font-weight: bold;
}

.metric-label {
  font-size: 12px;
  color: #718096;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  font-family: 'Courier New', monospace;
}

.metric-value.positive {
  color: #67c23a;
}

.metric-value.negative {
  color: #f56c6c;
}

/* 详细信息样式 */
.detail-info {
  display: flex;
  gap: 20px;
  min-width: 280px;
}

.detail-section {
  flex: 1;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
  text-align: center;
  padding: 4px 8px;
  background: #f7fafc;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 11px;
}

.detail-label {
  color: #718096;
}

.detail-value {
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.detail-value.income {
  color: #67c23a;
}

.detail-value.expense {
  color: #e6a23c;
}

.pagination-container {
  margin-top: 24px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics {
    gap: 16px;
  }

  .metric-item {
    min-width: 80px;
  }

  .detail-info {
    min-width: 240px;
  }
}

@media (max-width: 768px) {
  .ranking-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .business-info {
    width: 100%;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .metrics {
    width: 100%;
    justify-content: space-around;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .detail-info {
    width: 100%;
    flex-direction: column;
    gap: 12px;
  }
}
</style>
