from flask import Blueprint, jsonify, request
import jwt
import datetime
from ..utils.db_utils import get_connection
from functools import wraps
import os

# 创建 Blueprint
home_bp = Blueprint('home', __name__)

# 密钥 - 与 auth.py 保持一致
SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')

# 验证登录的装饰器
def login_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        return f(*args, **kwargs)
    return decorated

# 验证管理员权限的装饰器
def admin_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            is_admin = payload.get('is_admin', False)
            
            if not is_admin:
                return jsonify({
                    'code': 403,
                    'message': '权限不足',
                    'data': None
                }), 403
                
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        return f(*args, **kwargs)
    return decorated

# 个人统计数据接口
@home_bp.route('/personal-stats', methods=['GET'])
@login_required
def get_personal_stats():
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        token = auth_header.split(' ')[1]
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
        
        user_name = payload.get('name')
        month = request.args.get('month', datetime.datetime.now().strftime('%Y-%m'))
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询商务业绩数据
        query = """
        SELECT 
            income_order_count as orderCount,
            income_payment_amount as gmv,
            income_estimated_service_fee as serviceFee
        FROM business_performance
        WHERE business_name = %s AND month = %s
        """
        cursor.execute(query, (user_name, month))
        result = cursor.fetchone()
        
        if result:
            return jsonify({
                'code': 200,
                'message': '获取个人统计数据成功',
                'data': {
                    'orderCount': result['orderCount'],
                    'gmv': f"{float(result['gmv']):.2f}",
                    'serviceFee': f"{float(result['serviceFee']):.2f}"
                }
            })
        else:
            # 如果没有数据，返回默认值
            return jsonify({
                'code': 200,
                'message': '获取个人统计数据成功',
                'data': {
                    'orderCount': 0,
                    'gmv': '0.00',
                    'serviceFee': '0.00'
                }
            })
    except Exception as e:
        print(f"获取个人统计数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取个人统计数据失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 系统统计数据接口
@home_bp.route('/system-stats', methods=['GET'])
@login_required
def get_system_stats():
    connection = None
    cursor = None
    try:
        month = request.args.get('month', datetime.datetime.now().strftime('%Y-%m'))
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询总订单量
        query = """
        SELECT COUNT(*) as orderCount
        FROM `order`
        WHERE DATE_FORMAT(order_time, '%Y-%m') = %s AND order_status != 80
        """
        cursor.execute(query, (month,))
        order_count_result = cursor.fetchone()
        order_count = order_count_result['orderCount'] if order_count_result else 0
        
        # 查询总GMV
        query = """
        SELECT SUM(payment_amount) as totalGmv
        FROM `order`
        WHERE DATE_FORMAT(order_time, '%Y-%m') = %s AND order_status != 80
        """
        cursor.execute(query, (month,))
        gmv_result = cursor.fetchone()
        total_gmv = float(gmv_result['totalGmv'] or 0)
        
        # 查询总服务费，乘以0.85
        query = """
        SELECT SUM(o.payment_amount * REPLACE(p.investment_commission, '%', '') / 100 * 0.85) as totalServiceFee
        FROM `order` o
        LEFT JOIN product p ON o.product_id = p.product_id
        WHERE DATE_FORMAT(o.order_time, '%Y-%m') = %s AND o.order_status != 80
        """
        cursor.execute(query, (month,))
        service_fee_result = cursor.fetchone()
        total_service_fee = float(service_fee_result['totalServiceFee'] or 0)
        
        return jsonify({
            'code': 200,
            'message': '获取系统统计数据成功',
            'data': {
                'orderCount': order_count,
                'totalGmv': f"{total_gmv:.2f}",
                'totalServiceFee': f"{total_service_fee:.2f}"
            }
        })
    except Exception as e:
        print(f"获取系统统计数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取系统统计数据失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 商品统计接口
@home_bp.route('/product-stats', methods=['GET'])
@login_required
def get_product_stats():
    connection = None
    cursor = None
    try:
        month = request.args.get('month', datetime.datetime.now().strftime('%Y-%m'))
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询总商品数
        query = "SELECT COUNT(*) as totalCount FROM product"
        cursor.execute(query)
        total_count_result = cursor.fetchone()
        total_count = total_count_result['totalCount'] if total_count_result else 0
        
        # 查询新增商品数
        query = """
        SELECT COUNT(*) as newCount
        FROM product
        WHERE DATE_FORMAT(create_time, '%Y-%m') = %s
        """
        cursor.execute(query, (month,))
        new_count_result = cursor.fetchone()
        new_count = new_count_result['newCount'] if new_count_result else 0
        
        # 查询动销商品数（有订单的商品）
        query = """
        SELECT COUNT(DISTINCT o.product_id) as activeCount
        FROM `order` o
        WHERE DATE_FORMAT(o.order_time, '%Y-%m') = %s AND o.order_status != 80
        """
        cursor.execute(query, (month,))
        active_count_result = cursor.fetchone()
        active_count = active_count_result['activeCount'] if active_count_result else 0
        
        # 查询平均服务费率
        query = """
        SELECT AVG(REPLACE(investment_commission, '%', '')) as avgServiceFeeRate
        FROM product
        """
        cursor.execute(query)
        avg_service_fee_rate_result = cursor.fetchone()
        avg_service_fee_rate = round(float(avg_service_fee_rate_result['avgServiceFeeRate'] or 0))
        
        # 查询平均商家返佣率
        query = """
        SELECT AVG(merchant_commission_rate) as avgMerchantRate
        FROM product
        WHERE merchant_commission_rate IS NOT NULL
        """
        cursor.execute(query)
        avg_merchant_rate_result = cursor.fetchone()
        avg_merchant_rate = round(float(avg_merchant_rate_result['avgMerchantRate'] or 0))
        
        return jsonify({
            'code': 200,
            'message': '获取商品统计数据成功',
            'data': {
                'totalCount': total_count,
                'newCount': new_count,
                'activeCount': active_count,
                'avgServiceFeeRate': avg_service_fee_rate,
                'avgMerchantRate': avg_merchant_rate
            }
        })
    except Exception as e:
        print(f"获取商品统计数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取商品统计数据失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 订单统计接口
@home_bp.route('/order-stats', methods=['GET'])
@login_required
def get_order_stats():
    connection = None
    cursor = None
    try:
        month = request.args.get('month', datetime.datetime.now().strftime('%Y-%m'))
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询已付款订单数
        query = """
        SELECT COUNT(*) as paidCount
        FROM `order`
        WHERE DATE_FORMAT(order_time, '%Y-%m') = %s AND order_status = 30
        """
        cursor.execute(query, (month,))
        paid_count_result = cursor.fetchone()
        paid_count = paid_count_result['paidCount'] if paid_count_result else 0
        
        # 查询已发货订单数
        query = """
        SELECT COUNT(*) as shippedCount
        FROM `order`
        WHERE DATE_FORMAT(order_time, '%Y-%m') = %s AND is_shipped = 1
        """
        cursor.execute(query, (month,))
        shipped_count_result = cursor.fetchone()
        shipped_count = shipped_count_result['shippedCount'] if shipped_count_result else 0
        
        # 查询已结算订单数
        query = """
        SELECT COUNT(*) as settledCount
        FROM `order`
        WHERE DATE_FORMAT(order_time, '%Y-%m') = %s AND order_status = 60
        """
        cursor.execute(query, (month,))
        settled_count_result = cursor.fetchone()
        settled_count = settled_count_result['settledCount'] if settled_count_result else 0
        
        # 查询已失效订单数
        query = """
        SELECT COUNT(*) as invalidCount
        FROM `order`
        WHERE DATE_FORMAT(order_time, '%Y-%m') = %s AND order_status = 80
        """
        cursor.execute(query, (month,))
        invalid_count_result = cursor.fetchone()
        invalid_count = invalid_count_result['invalidCount'] if invalid_count_result else 0
        
        # 查询平均订单金额
        query = """
        SELECT AVG(payment_amount) as avgOrderAmount
        FROM `order`
        WHERE DATE_FORMAT(order_time, '%Y-%m') = %s AND order_status != 80
        """
        cursor.execute(query, (month,))
        avg_order_amount_result = cursor.fetchone()
        avg_order_amount = float(avg_order_amount_result['avgOrderAmount'] or 0)
        
        return jsonify({
            'code': 200,
            'message': '获取订单统计数据成功',
            'data': {
                'paidCount': paid_count,
                'shippedCount': shipped_count,
                'settledCount': settled_count,
                'invalidCount': invalid_count,
                'avgOrderAmount': f"{avg_order_amount:.2f}"
            }
        })
    except Exception as e:
        print(f"获取订单统计数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取订单统计数据失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 用户统计接口
@home_bp.route('/user-stats', methods=['GET'])
@login_required
def get_user_stats():
    connection = None
    cursor = None
    try:
        month = request.args.get('month', datetime.datetime.now().strftime('%Y-%m'))
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询商务用户数
        query = """
        SELECT COUNT(*) as businessCount
        FROM business_user
        WHERE role = 'business'
        """
        cursor.execute(query)
        business_count_result = cursor.fetchone()
        business_count = business_count_result['businessCount'] if business_count_result else 0
        
        # 查询运营用户数
        query = """
        SELECT COUNT(*) as operationCount
        FROM business_user
        WHERE role = 'operation'
        """
        cursor.execute(query)
        operation_count_result = cursor.fetchone()
        operation_count = operation_count_result['operationCount'] if operation_count_result else 0
        
        # 查询达人总数
        query = "SELECT COUNT(*) as talentCount FROM talent"
        cursor.execute(query)
        talent_count_result = cursor.fetchone()
        talent_count = talent_count_result['talentCount'] if talent_count_result else 0
        
        # 查询团长总数
        query = "SELECT COUNT(*) as teamLeaderCount FROM team_leader"
        cursor.execute(query)
        team_leader_count_result = cursor.fetchone()
        team_leader_count = team_leader_count_result['teamLeaderCount'] if team_leader_count_result else 0
        
        # 查询寄样申请数
        query = """
        SELECT COUNT(*) as sampleCount
        FROM product_sample
        WHERE DATE_FORMAT(create_time, '%Y-%m') = %s
        """
        cursor.execute(query, (month,))
        sample_count_result = cursor.fetchone()
        sample_count = sample_count_result['sampleCount'] if sample_count_result else 0
        
        return jsonify({
            'code': 200,
            'message': '获取用户统计数据成功',
            'data': {
                'businessCount': business_count,
                'operationCount': operation_count,
                'talentCount': talent_count,
                'teamLeaderCount': team_leader_count,
                'sampleCount': sample_count
            }
        })
    except Exception as e:
        print(f"获取用户统计数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取用户统计数据失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 图表数据接口
@home_bp.route('/chart-data', methods=['GET'])
@login_required
def get_chart_data():
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        token = auth_header.split(' ')[1]
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
        
        user_role = payload.get('role', 'business')
        user_name = payload.get('name')
        
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 构建查询条件
        date_condition = ""
        params = []
        
        if start_date and end_date:
            date_condition = "AND DATE(o.order_time) BETWEEN %s AND %s"
            params.extend([start_date, end_date])
        
        # 根据角色构建不同的查询
        business_condition = ""
        if user_role == 'business':
            business_condition = "AND o.talent_business = %s"
            params.append(user_name)
        
        # 查询每日数据，服务费乘以0.85
        query = f"""
        SELECT
            DATE(o.order_time) as date,
            SUM(o.payment_amount) as gmv,
            SUM(o.payment_amount * REPLACE(p.investment_commission, '%', '') / 100 * 0.85) as serviceFee,
            COUNT(*) as orderCount
        FROM `order` o
        LEFT JOIN product p ON o.product_id = p.product_id
        WHERE o.order_status != 80 {date_condition} {business_condition}
        GROUP BY DATE(o.order_time)
        ORDER BY DATE(o.order_time) ASC
        """
        cursor.execute(query, params)
        result = cursor.fetchall()
        
        # 格式化日期和数值
        for item in result:
            if isinstance(item['date'], datetime.date):
                item['date'] = item['date'].strftime('%m-%d')
            item['gmv'] = float(item['gmv'] or 0)
            item['serviceFee'] = float(item['serviceFee'] or 0)
            item['orderCount'] = int(item['orderCount'] or 0)
        
        return jsonify({
            'code': 200,
            'message': '获取图表数据成功',
            'data': result
        })
    except Exception as e:
        print(f"获取图表数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取图表数据失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 达人排行榜接口
@home_bp.route('/talent-ranking', methods=['GET'])
@login_required
def get_talent_ranking():
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        token = auth_header.split(' ')[1]
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])

        user_role = payload.get('role', 'business')
        user_name = payload.get('name')

        # 获取时间范围参数
        range_type = request.args.get('range', 'month')
        limit = int(request.args.get('limit', 10))

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 根据时间范围构建查询条件
        if range_type == 'week':
            # 计算本周的开始日期（周一）和结束日期（今天）
            today = datetime.datetime.now()
            # 获取本周一的日期（周一为0，周日为6）
            monday = today - datetime.timedelta(days=today.weekday())
            start_date = monday.strftime('%Y-%m-%d')
            end_date = today.strftime('%Y-%m-%d')

            print(f"本周排行榜查询范围: {start_date} 到 {end_date}")
            date_condition = "day BETWEEN %s AND %s"
            params = [start_date, end_date]
        else:
            # 默认为月份
            month = request.args.get('month', datetime.datetime.now().strftime('%Y-%m'))
            date_condition = "DATE_FORMAT(day, '%Y-%m') = %s"
            params = [month]

        # 查询达人排行榜（聚合多天数据）
        query = f"""
        SELECT
            promoter_id as id,
            promoter_name as name,
            SUM(payment_amount) as amount
        FROM promoter_performance
        WHERE {date_condition}
        GROUP BY promoter_id, promoter_name
        ORDER BY SUM(payment_amount) DESC
        LIMIT %s
        """
        cursor.execute(query, params + [limit])
        result = cursor.fetchall()

        # 格式化金额
        for item in result:
            item['amount'] = f"{float(item['amount']):.2f}"

        return jsonify({
            'code': 200,
            'message': '获取达人排行榜成功',
            'data': result
        })
    except Exception as e:
        print(f"获取达人排行榜失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取达人排行榜失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 爆品排行榜接口
@home_bp.route('/product-ranking', methods=['GET'])
@login_required
def get_product_ranking():
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        token = auth_header.split(' ')[1]
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])

        # 获取时间范围参数
        range_type = request.args.get('range', 'month')
        limit = int(request.args.get('limit', 10))

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 根据时间范围构建查询条件
        if range_type == 'week':
            # 计算本周的开始日期（周一）和结束日期（今天）
            today = datetime.datetime.now()
            # 获取本周一的日期（周一为0，周日为6）
            monday = today - datetime.timedelta(days=today.weekday())
            start_date = monday.strftime('%Y-%m-%d')
            end_date = today.strftime('%Y-%m-%d')

            print(f"爆品本周排行榜查询范围: {start_date} 到 {end_date}")
            date_condition = "DATE(o.order_time) BETWEEN %s AND %s"
            params = [start_date, end_date]
        else:
            # 默认为月份
            month = request.args.get('month', datetime.datetime.now().strftime('%Y-%m'))
            date_condition = "DATE_FORMAT(o.order_time, '%Y-%m') = %s"
            params = [month]

        # 查询爆品排行榜（按商品名称合并，和爆品模块保持一致）
        query = f"""
        SELECT
            MIN(o.product_id) as id,
            o.product_name as name,
            SUM(o.payment_amount) as amount,
            COUNT(*) as sales
        FROM `order` o
        WHERE {date_condition}
        AND o.order_status IN (30, 50, 60)
        GROUP BY o.product_name
        ORDER BY SUM(o.payment_amount) DESC
        LIMIT %s
        """
        cursor.execute(query, params + [limit])
        result = cursor.fetchall()

        # 格式化数据
        for item in result:
            item['amount'] = f"{float(item['amount']):.2f}"
            item['sales'] = str(item['sales'])

        return jsonify({
            'code': 200,
            'message': '获取爆品排行榜成功',
            'data': result
        })
    except Exception as e:
        print(f"获取爆品排行榜失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取爆品排行榜失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 商务排行榜接口
@home_bp.route('/business-ranking', methods=['GET'])
@login_required
def get_business_ranking():
    connection = None
    cursor = None
    try:
        # 获取时间范围参数
        range_type = request.args.get('range', 'month')
        limit = int(request.args.get('limit', 10))

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 根据时间范围构建查询条件
        if range_type == 'week':
            # 计算本周的开始日期（周一）和结束日期（今天）
            today = datetime.datetime.now()
            # 获取本周一的日期（周一为0，周日为6）
            monday = today - datetime.timedelta(days=today.weekday())
            start_date = monday.strftime('%Y-%m-%d')
            end_date = today.strftime('%Y-%m-%d')

            print(f"商务本周排行榜查询范围: {start_date} 到 {end_date}")
            date_condition = "day BETWEEN %s AND %s"
            params = [start_date, end_date]
        else:
            # 默认为月份
            month = request.args.get('month', datetime.datetime.now().strftime('%Y-%m'))
            date_condition = "DATE_FORMAT(day, '%Y-%m') = %s"
            params = [month]

        # 查询商务排行榜（聚合多天数据，关联用户表获取头像）
        query = f"""
        SELECT
            bp.business_name as name,
            bu.avatar,
            SUM(bp.net_estimated_service_fee) as amount
        FROM business_performance bp
        LEFT JOIN business_user bu ON bp.business_name = bu.name
        WHERE {date_condition.replace('day', 'bp.day')}
        GROUP BY bp.business_name, bu.avatar
        ORDER BY SUM(bp.net_estimated_service_fee) DESC
        LIMIT %s
        """
        cursor.execute(query, params + [limit])
        result = cursor.fetchall()

        # 格式化数据
        for item in result:
            item['amount'] = f"{float(item['amount']):.2f}"
            item['avatar'] = item.get('avatar') or '/assets/default-avatar.png'

        return jsonify({
            'code': 200,
            'message': '获取商务排行榜成功',
            'data': result
        })
    except Exception as e:
        print(f"获取商务排行榜失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取商务排行榜失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 通知列表接口
@home_bp.route('/notices', methods=['GET'])
@login_required
def get_notices():
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询通知列表
        query = """
        SELECT 
            id,
            title,
            content,
            DATE_FORMAT(create_time, '%Y-%m-%d') as time
        FROM system_notice
        ORDER BY create_time DESC
        LIMIT 10
        """
        cursor.execute(query)
        result = cursor.fetchall()
        
        return jsonify({
            'code': 200,
            'message': '获取通知列表成功',
            'data': result
        })
    except Exception as e:
        print(f"获取通知列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取通知列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 添加通知接口
@home_bp.route('/notices', methods=['POST'])
@admin_required
def add_notice():
    connection = None
    cursor = None
    try:
        data = request.json
        if not data or 'title' not in data or 'content' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供通知标题和内容',
                'data': None
            }), 400
        
        title = data['title']
        content = data['content']
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 插入通知
        query = """
        INSERT INTO system_notice (title, content, create_time)
        VALUES (%s, %s, NOW())
        """
        cursor.execute(query, (title, content))
        connection.commit()
        
        return jsonify({
            'code': 200,
            'message': '添加通知成功',
            'data': None
        })
    except Exception as e:
        print(f"添加通知失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'添加通知失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 删除通知接口
@home_bp.route('/notices/<notice_id>', methods=['DELETE'])
@admin_required
def delete_notice(notice_id):
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 删除通知
        query = "DELETE FROM system_notice WHERE id = %s"
        cursor.execute(query, (notice_id,))
        connection.commit()
        
        return jsonify({
            'code': 200,
            'message': '删除通知成功',
            'data': None
        })
    except Exception as e:
        print(f"删除通知失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'删除通知失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()